# Repository Guidelines

## Project Structure & Module Organization
- Frontend: `src/` (React + TypeScript, Vite). Entry: `index.html`, `src/main.tsx`, app shell in `src/App.tsx`, components in `src/components/`.
- Backend (Convex): `convex/` (TypeScript functions, schema, routes). Start point: `convex/router.ts`, data model in `convex/schema.ts`.
- Docs: `docs/` (design and Convex rules). Review before major changes.
- Config: `vite.config.ts`, `eslint.config.js`, `tailwind.config.js`, `tsconfig.*.json`. Local secrets in `.env.local`.

## Build, Test, and Development Commands
- `npm run dev`: Runs frontend (`vite`) and backend (`convex dev`) together.
- `npm run build`: Builds the production frontend bundle to `dist/`.
- `npm run lint`: Type-checks (`tsc` for app and convex), runs Convex once to validate routes, then builds to ensure compile-time soundness.
- Backend-only: `convex dev` (in parallel with `vite` if needed).

## Coding Style & Naming Conventions
- Language: TypeScript (strict-ish via ESLint config). React 19 with Vite.
- Formatting: Use Prettier defaults; 2-space indent; LF line endings.
- Linting: ESLint with TypeScript + React hooks rules. Fix warnings before PR.
- Filenames: React components PascalCase (`ProfileSetup.tsx`). Utilities camelCase (`lib/utils.ts`). Convex modules lower-case (`leads.ts`, `users.ts`).
- CSS: TailwindCSS; prefer utility classes in components; shared styles in `src/index.css`.

## Testing Guidelines
- Current status: No formal test runner configured.
- Expectations: Maintain zero type errors and lint warnings; add focused unit tests when introducing complex logic.
- Suggested pattern (if adding tests): Vitest with co-located files `*.test.ts[x]` near sources in `src/` or `convex/`.

## Commit & Pull Request Guidelines
- Commits: Imperative mood, concise scope. Example: `feat(referrals): generate unique referral links`.
- PRs: Include purpose, key changes, verification steps, and any screenshots for UI. Link issues. Note schema or API changes touching `convex/`.

## Security & Configuration Tips
- Secrets: Use `.env.local`; never commit credentials. Review `docs/CONVEX-RULES.md` for access patterns.
- Convex: Update `convex/auth.config.ts` before production; validate routes in `convex/router.ts`.
- Aliases: Import from `@/` for `src/` paths as configured in `vite.config.ts`.
