## UI Cleanup Plan (Leads Tab First)

- [x] Investigate warm/approved badge padding on Leads card header
- [x] Add compact status badge style and apply to Warm/Approved pills to remove excess right padding
- [x] Split emoji and label into separate spans to avoid emoji width causing uneven spacing
- [ ] Quick pass: audit other paddings/margins in Leads (notes, contact rows, actions column)
- [ ] If needed, introduce a small consistent gap utility for inline pills and re-apply
- [ ] Verify in multiple viewports (sm, md, lg, xl) and dark theme
- [ ] If issues found, patch and re-verify

Changelog
- Added .status-badge-compact (tighter horizontal padding; nowrap) in src/index.css
- Updated Leads.tsx to use compact badges and normalized inner content with icon + text spans
- Added robust console logging in submit/approve/status handlers for easier UI debugging

Next steps
- Open the app and visually verify the Leads tab in sm/md/lg widths
- Note any remaining spacing discrepancies (e.g., actions column min width, grid gaps) and fix

Notes
- Keep all spacing on the 8pt system (4/8/12/16/24/32)
- Prefer content-driven sizing for pills; avoid arbitrary min-widths

