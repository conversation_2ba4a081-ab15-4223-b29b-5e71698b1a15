import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

// List all pending partner applications
export const listPendingApplications = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("users"),
    name: v.optional(v.string()),
    fullName: v.optional(v.string()),
    email: v.optional(v.string()),
    companyName: v.optional(v.string()),
    companyType: v.optional(v.string()),
    roleTitle: v.optional(v.string()),
    telegram: v.optional(v.string()),
    whatsapp: v.optional(v.string()),
    xProfile: v.optional(v.string()),
    preferredCommunication: v.optional(v.array(v.string())),
    profileCompleted: v.optional(v.boolean()),
    _creationTime: v.number(),
  })),
  handler: async (ctx) => {
    console.log("📋 Fetching pending partner applications...");

    const pendingPartners = await ctx.db
      .query("users")
      .filter((q) =>
        q.and(
          q.eq(q.field("role"), "partner"),
          q.eq(q.field("approved"), undefined)
        )
      )
      .collect();

    console.log(`📊 Found ${pendingPartners.length} pending partner applications:`,
      pendingPartners.map(p => ({
        id: p._id,
        email: p.email,
        name: p.fullName || p.name,
        profileCompleted: p.profileCompleted,
        approved: p.approved
      }))
    );

    return pendingPartners.map(partner => ({
      _id: partner._id,
      name: partner.name,
      fullName: partner.fullName,
      email: partner.email,
      companyName: partner.companyName,
      companyType: partner.companyType,
      roleTitle: partner.roleTitle,
      telegram: partner.telegram,
      whatsapp: partner.whatsapp,
      xProfile: partner.xProfile,
      preferredCommunication: partner.preferredCommunication,
      profileCompleted: partner.profileCompleted,
      _creationTime: partner._creationTime,
    }));
  },
});

// List all approved partners
export const listApprovedPartners = query({
  args: {},
  returns: v.array(v.object({
    _id: v.id("users"),
    name: v.optional(v.string()),
    fullName: v.optional(v.string()),
    email: v.optional(v.string()),
    companyName: v.optional(v.string()),
    tier: v.optional(v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond"))),
    assignedSalesId: v.optional(v.string()),
    assignedSalesName: v.optional(v.string()),
    _creationTime: v.number(),
  })),
  handler: async (ctx) => {
    console.log("📋 Fetching approved partners...");
    
    const approvedPartners = await ctx.db
      .query("users")
      .filter((q) =>
        q.and(
          q.eq(q.field("role"), "partner"),
          q.eq(q.field("approved"), true),
          q.eq(q.field("softDeletedAt"), undefined)
        )
      )
      .collect();

    // Get assignments for each partner
    const assignments = await ctx.db.query("assignments").collect();
    const assignmentMap = assignments.reduce((acc, assignment) => {
      acc[assignment.partnerId] = assignment.salesUserId;
      return acc;
    }, {} as Record<string, string>);

    // Get sales member names
    const salesMembers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("role"), "sales"))
      .collect();
    
    const salesMemberMap = salesMembers.reduce((acc, member) => {
      acc[member._id] = member.name || member.fullName || "Unknown";
      return acc;
    }, {} as Record<string, string>);

    console.log(`📊 Found ${approvedPartners.length} approved partners`);
    
    return approvedPartners.map(partner => ({
      _id: partner._id,
      name: partner.name,
      fullName: partner.fullName,
      email: partner.email,
      companyName: partner.companyName,
      tier: partner.tier,
      assignedSalesId: assignmentMap[partner._id],
      assignedSalesName: assignmentMap[partner._id] ? salesMemberMap[assignmentMap[partner._id]] : undefined,
      _creationTime: partner._creationTime,
    }));
  },
});

// Approve or reject a partner application
export const reviewPartnerApplication = mutation({
  args: {
    partnerId: v.id("users"),
    approved: v.boolean(),
    tier: v.optional(v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond"))),
    salesUserId: v.optional(v.id("users")),
    notes: v.optional(v.string()),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, { partnerId, approved, tier, salesUserId, notes }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`🔍 Reviewing partner application: ${partnerId} - ${approved ? 'APPROVE' : 'REJECT'}`);
    
    const partner = await ctx.db.get(partnerId);
    if (!partner) {
      console.error(`❌ Partner not found: ${partnerId}`);
      throw new Error("Partner not found");
    }

    if (partner.role !== "partner") {
      console.error(`❌ User ${partnerId} is not a partner`);
      throw new Error("User is not a partner");
    }

    // Update partner approval status
    const updates: any = { approved };
    
    if (approved) {
      // Set tier if provided, otherwise default to trusted
      updates.tier = tier || "trusted";
      
      console.log(`✅ Approving partner with tier: ${updates.tier}`);
    } else {
      console.log(`❌ Rejecting partner application`);
    }

    await ctx.db.patch(partnerId, updates);

    // If approved and sales user assigned, create assignment
    if (approved && salesUserId) {
      console.log(`👥 Assigning partner to sales member: ${salesUserId}`);
      
      // Verify sales user exists and is a sales member
      const salesUser = await ctx.db.get(salesUserId);
      if (!salesUser || salesUser.role !== "sales") {
        console.error(`❌ Invalid sales user: ${salesUserId}`);
        throw new Error("Invalid sales user");
      }

      // Remove existing assignment if any
      const existingAssignment = await ctx.db
        .query("assignments")
        .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
        .first();
        
      if (existingAssignment) {
        await ctx.db.delete(existingAssignment._id);
      }

      // Create new assignment
      await ctx.db.insert("assignments", {
        partnerId,
        salesUserId,
      });

      // Update existing leads with the sales assignment
      const leads = await ctx.db
        .query("leads")
        .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
        .collect();
        
      for (const lead of leads) {
        await ctx.db.patch(lead._id, { salesUserId });
      }

      console.log(`✅ Partner assigned to sales member, updated ${leads.length} leads`);
    }

    // Log the review action
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: approved ? "PARTNER_APPROVED" : "PARTNER_REJECTED",
      entity: `users/${partnerId}`,
      meta: { 
        tier: approved ? (tier || "trusted") : undefined,
        salesUserId,
        notes,
        reviewedBy: ops.name || ops.email
      },
      at: Date.now(),
    });

    console.log(`🎉 Partner application review completed: ${partnerId}`);
    
    return { success: true };
  },
});

// Update partner tier
export const updatePartnerTier = mutation({
  args: {
    partnerId: v.id("users"),
    tier: v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond")),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, { partnerId, tier }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`🏆 Updating partner tier: ${partnerId} -> ${tier}`);
    
    const partner = await ctx.db.get(partnerId);
    if (!partner) {
      console.error(`❌ Partner not found: ${partnerId}`);
      throw new Error("Partner not found");
    }

    if (partner.role !== "partner" || !partner.approved) {
      console.error(`❌ User ${partnerId} is not an approved partner`);
      throw new Error("User is not an approved partner");
    }

    await ctx.db.patch(partnerId, { tier });

    // Log the tier update
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "PARTNER_TIER_UPDATED",
      entity: `users/${partnerId}`,
      meta: { 
        oldTier: partner.tier,
        newTier: tier,
        updatedBy: ops.name || ops.email
      },
      at: Date.now(),
    });

    console.log(`✅ Partner tier updated successfully: ${partnerId}`);
    
    return { success: true };
  },
});

// Assign partner to sales member
export const assignPartnerToSales = mutation({
  args: {
    partnerId: v.id("users"),
    salesUserId: v.id("users"),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, { partnerId, salesUserId }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    console.log(`👥 Assigning partner ${partnerId} to sales member ${salesUserId}`);
    
    // Verify partner exists and is approved
    const partner = await ctx.db.get(partnerId);
    if (!partner || partner.role !== "partner" || !partner.approved) {
      console.error(`❌ Invalid partner: ${partnerId}`);
      throw new Error("Invalid partner");
    }

    // Verify sales user exists and is a sales member
    const salesUser = await ctx.db.get(salesUserId);
    if (!salesUser || salesUser.role !== "sales") {
      console.error(`❌ Invalid sales user: ${salesUserId}`);
      throw new Error("Invalid sales user");
    }

    // Remove existing assignment if any
    const existingAssignment = await ctx.db
      .query("assignments")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .first();
      
    if (existingAssignment) {
      await ctx.db.delete(existingAssignment._id);
    }

    // Create new assignment
    const assignmentId = await ctx.db.insert("assignments", {
      partnerId,
      salesUserId,
    });

    // Update existing leads with the sales assignment
    const leads = await ctx.db
      .query("leads")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .collect();
      
    for (const lead of leads) {
      await ctx.db.patch(lead._id, { salesUserId });
    }

    // Log the assignment
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "PARTNER_ASSIGNED_TO_SALES",
      entity: `assignments/${assignmentId}`,
      meta: { 
        partnerId,
        salesUserId,
        partnerName: partner.name || partner.fullName,
        salesName: salesUser.name || salesUser.fullName,
        updatedLeads: leads.length
      },
      at: Date.now(),
    });

    console.log(`✅ Partner assignment completed, updated ${leads.length} leads`);
    
    return { success: true };
  },
});

// Get partner application statistics
export const getApplicationStats = query({
  args: {},
  returns: v.object({
    total: v.number(),
    pending: v.number(),
    approved: v.number(),
    rejected: v.number(),
    tierCounts: v.record(v.string(), v.number()),
  }),
  handler: async (ctx) => {
    console.log("📊 Calculating partner application statistics...");
    
    const allPartners = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("role"), "partner"))
      .collect();

    const pending = allPartners.filter(p => p.approved === undefined).length;
    const approved = allPartners.filter(p => p.approved === true).length;
    const rejected = allPartners.filter(p => p.approved === false).length;
    
    const tierCounts = approved > 0 ? allPartners
      .filter(p => p.approved === true)
      .reduce((acc, partner) => {
        const tier = partner.tier || "trusted";
        acc[tier] = (acc[tier] || 0) + 1;
        return acc;
      }, {} as Record<string, number>) : {};

    console.log(`📈 Application stats: ${pending} pending, ${approved} approved, ${rejected} rejected`);
    
    return {
      total: allPartners.length,
      pending,
      approved,
      rejected,
      tierCounts,
    };
  },
});

// Remove/deactivate a partner
export const removePartner = mutation({
  args: {
    partnerId: v.id("users"),
  },
  returns: v.object({ success: v.boolean() }),
  handler: async (ctx, { partnerId }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);

    console.log(`🗑️ Removing partner: ${partnerId}`);

    const partner = await ctx.db.get(partnerId);
    if (!partner) {
      console.error(`❌ Partner not found: ${partnerId}`);
      throw new Error("Partner not found");
    }

    if (partner.role !== "partner") {
      console.error(`❌ User ${partnerId} is not a partner`);
      throw new Error("User is not a partner");
    }

    // Soft delete by setting approved to false and adding deletion timestamp
    await ctx.db.patch(partnerId, {
      approved: false,
      softDeletedAt: Date.now()
    });

    // Remove any sales assignments
    const assignment = await ctx.db
      .query("assignments")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .first();

    if (assignment) {
      await ctx.db.delete(assignment._id);
      console.log(`🔗 Removed sales assignment for partner: ${partnerId}`);
    }

    // Log the removal action
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: "PARTNER_REMOVED",
      entity: `users/${partnerId}`,
      meta: {
        partnerName: partner.name || partner.fullName,
        removedBy: ops.name || ops.email
      },
      at: Date.now(),
    });

    console.log(`✅ Partner removed successfully: ${partnerId}`);

    return { success: true };
  },
});

// DEBUG: Create test partner applications
export const createTestPartnerApplications = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🧪 Creating test partner applications...");

    // Create 3 test partner applications
    const testPartners = [
      {
        name: "John Smith",
        fullName: "John Smith",
        email: "<EMAIL>",
        companyName: "Tech Solutions Inc",
        companyType: "Technology",
        roleTitle: "CEO",
        telegram: "@johnsmith",
        whatsapp: "+1234567890",
        xProfile: "@johnsmith_tech",
        preferredCommunication: ["email", "telegram"] as ("email" | "whatsapp" | "telegram")[],
        role: "partner" as const,
        approved: undefined,
        profileCompleted: true,
        tier: "trusted" as const
      },
      {
        name: "Sarah Johnson",
        fullName: "Sarah Johnson",
        email: "<EMAIL>",
        companyName: "Digital Marketing Pro",
        companyType: "Marketing",
        roleTitle: "Founder",
        telegram: "@sarahj",
        whatsapp: "+1234567891",
        xProfile: "@sarahj_marketing",
        preferredCommunication: ["email", "whatsapp"] as ("email" | "whatsapp" | "telegram")[],
        role: "partner" as const,
        approved: undefined,
        profileCompleted: true,
        tier: "trusted" as const
      },
      {
        name: "Mike Chen",
        fullName: "Mike Chen",
        email: "<EMAIL>",
        companyName: "Blockchain Ventures",
        companyType: "Venture Capital",
        roleTitle: "Partner",
        telegram: "@mikechen",
        whatsapp: "+1234567892",
        xProfile: "@mikechen_vc",
        preferredCommunication: ["telegram", "email"] as ("email" | "whatsapp" | "telegram")[],
        role: "partner" as const,
        approved: undefined,
        profileCompleted: false,
        tier: "trusted" as const
      }
    ];

    const createdIds = [];
    for (const partner of testPartners) {
      const id = await ctx.db.insert("users", partner);
      createdIds.push(id);
      console.log(`✅ Created test partner: ${partner.name} (${id})`);
    }

    console.log(`🎉 Created ${createdIds.length} test partner applications`);
    return { success: true, createdIds };
  },
});

// DEBUG: Check user status by email
export const debugUserStatus = query({
  args: { email: v.string() },
  handler: async (ctx, { email }) => {
    console.log(`🔍 Debugging user status for email: ${email}`);

    const user = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), email))
      .first();

    if (!user) {
      console.log(`❌ No user found with email: ${email}`);
      return { found: false };
    }

    console.log(`✅ Found user:`, {
      id: user._id,
      email: user.email,
      name: user.fullName || user.name,
      role: user.role,
      approved: user.approved,
      profileCompleted: user.profileCompleted,
      creationTime: user._creationTime
    });

    return {
      found: true,
      user: {
        id: user._id,
        email: user.email,
        name: user.fullName || user.name,
        role: user.role,
        approved: user.approved,
        profileCompleted: user.profileCompleted,
        creationTime: user._creationTime,
        companyName: user.companyName,
        tier: user.tier
      }
    };
  },
});

// ADMIN SCRIPT: Set all non-admin users as pending applicants
export const setAllUsersPending = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🔧 ADMIN SCRIPT: Setting all non-admin users as pending applicants...");

    // Get all <NAME_EMAIL>
    const allUsers = await ctx.db
      .query("users")
      .filter((q) => q.neq(q.field("email"), "<EMAIL>"))
      .collect();

    console.log(`📊 Found ${allUsers.length} non-admin users to process`);

    let updatedCount = 0;

    for (const user of allUsers) {
      // Set as partner role and pending approval
      await ctx.db.patch(user._id, {
        role: "partner",
        approved: undefined, // Pending approval
        profileCompleted: true, // Assume profile is completed
      });

      updatedCount++;
      console.log(`✅ Updated user ${user.email || user._id} - now pending approval`);
    }

    console.log(`🎉 Successfully updated ${updatedCount} users as pending applicants`);

    return {
      success: true,
      updatedCount,
      message: `Updated ${updatedCount} users as pending partner applicants`
    };
  },
});

// ADMIN SCRIPT: Create a realistic test partner application
export const createTestPartnerApplication = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🧪 ADMIN SCRIPT: Creating test partner application...");

    // Create a realistic test partner application
    const testPartner = {
      email: "<EMAIL>",
      name: "Francesco Oddo",
      fullName: "Francesco Oddo",
      companyName: "IBC Group",
      companyType: "Venture Capital (VC)",
      roleTitle: "CEO",
      telegram: "@francesco_ibc",
      whatsapp: "+***********",
      xProfile: "https://x.com/francesco_oddo",
      emailDetails: "Prefer email communication during business hours (9 AM - 6 PM CET). Available for urgent matters via Telegram.",
      preferredCommunication: ["email", "telegram", "whatsapp"] as ("email" | "whatsapp" | "telegram")[],
      preferredPaymentMethod: "bank" as const,
      role: "partner" as const,
      approved: undefined, // Pending approval
      profileCompleted: true,
      tier: "trusted" as const,
      defaultReferralCode: "francescoddo",
      termsAcceptedVersion: "1.0",
      termsAcceptedAt: Date.now(),
    };

    // Check if user already exists
    const existingUser = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("email"), testPartner.email))
      .first();

    let userId;

    if (existingUser) {
      console.log(`📝 Updating existing user: ${testPartner.email}`);
      await ctx.db.patch(existingUser._id, testPartner);
      userId = existingUser._id;
    } else {
      console.log(`➕ Creating new user: ${testPartner.email}`);
      userId = await ctx.db.insert("users", testPartner);
    }

    // Create referral link
    const existingLink = await ctx.db
      .query("referralLinks")
      .filter((q) => q.eq(q.field("code"), testPartner.defaultReferralCode))
      .first();

    if (!existingLink) {
      await ctx.db.insert("referralLinks", {
        ownerId: userId,
        code: testPartner.defaultReferralCode,
        destinationUrl: `/submit-lead?ref=${testPartner.defaultReferralCode}`,
        title: `${testPartner.fullName}'s Referral`,
      });
      console.log(`🔗 Created referral link: ${testPartner.defaultReferralCode}`);
    }

    // Log the profile completion
    await ctx.db.insert("auditLogs", {
      actorUserId: userId,
      action: "PROFILE_COMPLETED",
      entity: `users/${userId}`,
      meta: {
        companyName: testPartner.companyName,
        referralCode: testPartner.defaultReferralCode,
        source: "ADMIN_SCRIPT"
      },
      at: Date.now(),
    });

    console.log(`🎉 Successfully created test partner application for ${testPartner.email}`);

    return {
      success: true,
      userId,
      email: testPartner.email,
      referralCode: testPartner.defaultReferralCode,
      message: `Created test partner application for ${testPartner.email}`
    };
  },
});



// MIGRATION: Fix users with missing roles
export const fixUsersWithMissingRoles = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🔄 MIGRATION: Fixing users with missing roles...");

    // Find all users who have completed their profile but don't have a role set
    const allUsers = await ctx.db.query("users").collect();
    
    const usersToFix = allUsers.filter(user => 
      user.profileCompleted === true && 
      (!user.role || user.role === undefined) &&
      user.email !== "<EMAIL>" // Skip admin user
    );

    console.log(`📊 Found ${usersToFix.length} users with missing roles:`, 
      usersToFix.map(u => ({ id: u._id, email: u.email, role: u.role }))
    );

    let fixedCount = 0;
    for (const user of usersToFix) {
      await ctx.db.patch(user._id, { 
        role: "partner",
        approved: user.approved === undefined ? undefined : user.approved // Keep existing approval status
      });
      fixedCount++;
      console.log(`✅ Fixed user: ${user.email || user._id} - set role to 'partner'`);
    }

    console.log(`🎉 Migration completed: Fixed ${fixedCount} users with missing roles`);

    return {
      success: true,
      fixedCount,
      message: `Fixed ${fixedCount} users with missing partner roles`
    };
  },
});

// DEBUG: Clean up test data
export const cleanupTestData = mutation({
  args: {},
  handler: async (ctx) => {
    console.log("🧹 Cleaning up test partner data...");

    const testEmails = [
      "<EMAIL>",
      "<EMAIL>",
      "<EMAIL>"
    ];

    let deletedCount = 0;
    for (const email of testEmails) {
      const user = await ctx.db
        .query("users")
        .filter((q) => q.eq(q.field("email"), email))
        .first();

      if (user) {
        await ctx.db.delete(user._id);
        deletedCount++;
        console.log(`🗑️ Deleted test user: ${email}`);
      }
    }

    console.log(`✅ Cleaned up ${deletedCount} test users`);
    return { success: true, deletedCount };
  },
});
