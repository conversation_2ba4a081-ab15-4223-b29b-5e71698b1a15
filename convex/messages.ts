import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const send = mutation({
  args: {
    toUserId: v.optional(v.id("users")),
    body: v.string(),
    kind: v.union(v.literal("feedback"), v.literal("support")),
  },
  handler: async (ctx, args) => {
    const user = await requireUser(ctx, "any");
    
    if (args.body.length > 2000) {
      throw new Error("Message too long (max 2000 characters)");
    }
    
    const messageId = await ctx.db.insert("messages", {
      fromUserId: user._id,
      toUserId: args.toUserId,
      body: args.body,
      kind: args.kind,
      resolved: false,
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "MESSAGE_SENT",
      entity: `messages/${messageId}`,
      meta: { kind: args.kind, toUserId: args.toUserId },
      at: Date.now(),
    });
    
    return messageId;
  },
});

export const listMine = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireUser(ctx, "any");
    
    const sentMessages = await ctx.db
      .query("messages")
      .withIndex("by_from", (q) => q.eq("fromUserId", user._id))
      .collect();
      
    const receivedMessages = await ctx.db
      .query("messages")
      .withIndex("by_to", (q) => q.eq("toUserId", user._id))
      .collect();
    
    return {
      sent: sentMessages,
      received: receivedMessages,
    };
  },
});

export const listAll = query({
  args: { resolved: v.optional(v.boolean()) },
  handler: async (ctx, { resolved }) => {
    await requireUser(ctx, ["admin", "superadmin", "ops", "sales"]);
    
    const messages = await ctx.db.query("messages").collect();
    
    if (resolved !== undefined) {
      return messages.filter(m => m.resolved === resolved);
    }
    
    return messages;
  },
});

export const markResolved = mutation({
  args: { 
    messageId: v.id("messages"),
    resolved: v.boolean(),
  },
  handler: async (ctx, { messageId, resolved }) => {
    const user = await requireUser(ctx, ["admin", "superadmin", "ops", "sales"]);
    
    await ctx.db.patch(messageId, { resolved });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: resolved ? "MESSAGE_RESOLVED" : "MESSAGE_REOPENED",
      entity: `messages/${messageId}`,
      at: Date.now(),
    });
    
    return { ok: true };
  },
});
