import { httpRouter } from "convex/server";
import { httpAction } from "./_generated/server";
import { api } from "./_generated/api";

const http = httpRouter();

// Referral link redirect with click tracking
http.route({
  pathPrefix: "/r/",
  method: "GET",
  handler: httpAction(async (ctx, req) => {
    const url = new URL(req.url);
    const code = url.pathname.split("/r/")[1];
    
    if (!code) {
      return new Response("Invalid referral code", { status: 400 });
    }
    
    const link = await ctx.runQuery(api.referrals.getLinkByCode, { code });
    if (!link) {
      return new Response("Referral link not found", { status: 404 });
    }

    // Record the click
    await ctx.runMutation(api.referrals.trackClick, {
      code: link.code,
      ip: req.headers.get("x-forwarded-for") ?? undefined,
      userAgent: req.headers.get("user-agent") ?? undefined,
      utm: {
        source: url.searchParams.get("utm_source") ?? undefined,
        medium: url.searchParams.get("utm_medium") ?? undefined,
        campaign: url.searchParams.get("utm_campaign") ?? undefined,
      },
    });

    return Response.redirect(link.destinationUrl, 302);
  }),
});

export default http;
