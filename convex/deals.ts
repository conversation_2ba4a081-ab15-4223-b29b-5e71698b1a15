import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const create = mutation({
  args: {
    partnerId: v.id("users"),
    leadId: v.optional(v.id("leads")),
    projectName: v.string(),
    dealType: v.string(),
    status: v.union(v.literal("in_progress"), v.literal("closed"), v.literal("lost"), v.literal("paid")),
    totalTokens: v.optional(v.number()),
    receivedTokens: v.optional(v.number()),
    liquidatedTokens: v.optional(v.number()),
    liquidationUsd: v.optional(v.number()),
    dealValueUsd: v.optional(v.number()),
    commissionPct: v.number(),
    commissionDueTokenUsd: v.optional(v.number()),
    commissionDueFiatUsd: v.optional(v.number()),
    commissionPendingUsd: v.optional(v.number()),
    launched: v.optional(v.boolean()),
    closedBy: v.optional(v.string()),
    source: v.optional(v.string()),
    month: v.optional(v.number()),
    year: v.optional(v.number()),
    paidFiatUsd: v.optional(v.number()),
    remainingFiatUsd: v.optional(v.number()),
    averageSellingPrice: v.optional(v.number()),
  },
  handler: async (ctx, args) => {
    const internal = await requireUser(ctx, ["sales", "ops", "accounting", "admin", "superadmin"]);
    
    // Auto-calculate commission if deal is created as closed/paid and has value
    let finalArgs = { ...args };
    
    const isClosedDeal = args.status === "closed" || args.status === "paid";
    
    if (isClosedDeal && args.dealValueUsd && args.dealValueUsd > 0) {
      // Only auto-calculate if commission isn't manually set
      if (!args.commissionDueFiatUsd) {
        // Get partner information for commission calculation
        const partner = await ctx.db.get(args.partnerId);
        if (!partner) {
          throw new Error("Partner not found");
        }
        
        // Commission rates based on partner tier
        const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
          switch (tier) {
            case "diamond": return 10; // 10%
            case "elite": return 7.5;  // 7.5%
            case "trusted": return 5;  // 5%
            default: return 5;         // 5% for no tier
          }
        };
        
        const rate = getCommissionRate(partner.tier);
        const calculatedCommission = args.dealValueUsd * (rate / 100);
        
        finalArgs.commissionDueFiatUsd = calculatedCommission;
        finalArgs.commissionPct = rate;
        
        console.log(`Auto-calculated commission on create for ${partner.name || partner.email}: $${calculatedCommission} at ${rate}% (${partner.tier || 'no tier'})`);
      }
    }
    
    // Auto-set month and year if not provided
    const now = new Date();
    if (!finalArgs.month) {
      finalArgs.month = now.getMonth() + 1; // JavaScript months are 0-indexed
    }
    if (!finalArgs.year) {
      finalArgs.year = now.getFullYear();
    }

    const dealId = await ctx.db.insert("deals", {
      ...finalArgs,
      salesUserId: internal._id,
      lastUpdatedAt: Date.now(),
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: internal._id,
      action: "DEAL_CREATED",
      entity: `deals/${dealId}`,
      meta: { projectName: finalArgs.projectName, dealValueUsd: finalArgs.dealValueUsd, commissionCalculated: isClosedDeal },
      at: Date.now(),
    });
    
    return dealId;
  },
});

export const update = mutation({
  args: {
    dealId: v.id("deals"),
    status: v.optional(v.union(v.literal("in_progress"), v.literal("closed"), v.literal("lost"), v.literal("paid"))),
    totalTokens: v.optional(v.number()),
    receivedTokens: v.optional(v.number()),
    liquidatedTokens: v.optional(v.number()),
    liquidationUsd: v.optional(v.number()),
    dealValueUsd: v.optional(v.number()),
    commissionPct: v.optional(v.number()),
    commissionDueTokenUsd: v.optional(v.number()),
    commissionDueFiatUsd: v.optional(v.number()),
    commissionPendingUsd: v.optional(v.number()),
    launched: v.optional(v.boolean()),
    closedBy: v.optional(v.string()),
    source: v.optional(v.string()),
    month: v.optional(v.number()),
    year: v.optional(v.number()),
    paidFiatUsd: v.optional(v.number()),
    remainingFiatUsd: v.optional(v.number()),
    averageSellingPrice: v.optional(v.number()),
  },
  handler: async (ctx, { dealId, ...updates }) => {
    const internal = await requireUser(ctx, ["sales", "ops", "accounting", "admin", "superadmin"]);
    
    // Get current deal data
    const currentDeal = await ctx.db.get(dealId);
    if (!currentDeal) {
      throw new Error("Deal not found");
    }
    
    // Get partner information for commission calculation
    const partner = await ctx.db.get(currentDeal.partnerId);
    if (!partner) {
      throw new Error("Partner not found");
    }
    
    // Commission rates based on partner tier
    const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
      switch (tier) {
        case "diamond": return 10; // 10%
        case "elite": return 7.5;  // 7.5%
        case "trusted": return 5;  // 5%
        default: return 5;         // 5% for no tier
      }
    };
    
    // Auto-calculate commission if deal is being closed and has value
    let finalUpdates = { ...updates };
    
    const isClosingDeal = updates.status === "closed" || updates.status === "paid";
    const dealValue = updates.dealValueUsd || currentDeal.dealValueUsd;
    
    if (isClosingDeal && dealValue && dealValue > 0) {
      // Only auto-calculate if commission isn't manually set
      if (!updates.commissionDueFiatUsd && !currentDeal.commissionDueFiatUsd) {
        const rate = getCommissionRate(partner.tier);
        const calculatedCommission = dealValue * (rate / 100);
        
        finalUpdates.commissionDueFiatUsd = calculatedCommission;
        finalUpdates.commissionPct = rate;
        
        console.log(`Auto-calculated commission for ${partner.name || partner.email}: $${calculatedCommission} at ${rate}% (${partner.tier || 'no tier'})`);
      }
    }
    
    await ctx.db.patch(dealId, {
      ...finalUpdates,
      lastUpdatedAt: Date.now(),
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: internal._id,
      action: "DEAL_UPDATED",
      entity: `deals/${dealId}`,
      meta: finalUpdates,
      at: Date.now(),
    });
    
    return { ok: true };
  },
});

// Helper function to get assigned partner IDs for a sales user
const getAssignedPartnerIds = async (ctx: any, salesUserId: string) => {
  const assignments = await ctx.db
    .query("assignments")
    .withIndex("by_sales", (q: any) => q.eq("salesUserId", salesUserId))
    .collect();
  
  return assignments.map((assignment: any) => assignment.partnerId);
};

export const listForPartner = query({
  args: { 
    partnerId: v.optional(v.id("users")), 
    status: v.optional(v.string()) 
  },
  handler: async (ctx, { partnerId, status }) => {
    const viewer = await requireUser(ctx, "any");
    
    // Determine which partner's deals to show
    let targetPartnerId = partnerId;
    if (viewer.role === "partner") {
      targetPartnerId = viewer._id; // Partners can only see their own
    }
    
    // Handle sales users - they can only see deals from their assigned partners
    if (viewer.role === "sales") {
      const assignedPartnerIds = await getAssignedPartnerIds(ctx, viewer._id);
      
      if (assignedPartnerIds.length === 0) {
        // Sales user has no assigned partners, return empty array
        return [];
      }
      
      if (targetPartnerId) {
        // Check if the requested partner is assigned to this sales user
        if (!assignedPartnerIds.includes(targetPartnerId)) {
          throw new Error("Unauthorized: You can only view deals from your assigned partners");
        }
        
        const deals = await ctx.db
          .query("deals")
          .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
          .collect();
        
        return status ? deals.filter(d => d.status === status) : deals;
      }
      
      // No specific partner requested, get deals from all assigned partners
      const allDeals = [];
      for (const partnerIdStr of assignedPartnerIds) {
        const partnerId = partnerIdStr as any; // Type assertion for Convex ID
        const partnerDeals = await ctx.db
          .query("deals")
          .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
          .collect();
        allDeals.push(...partnerDeals);
      }
      
      return status ? allDeals.filter(d => d.status === status) : allDeals;
    }
    
    if (!targetPartnerId) {
      // Internal users (admin, ops, accounting, superadmin) without specifying partnerId see all
      const deals = await ctx.db.query("deals").collect();
      return status ? deals.filter(d => d.status === status) : deals;
    }
    
    const deals = await ctx.db
      .query("deals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
      
    return status ? deals.filter(d => d.status === status) : deals;
  }
});

export const getDealSummary = query({
  args: { partnerId: v.optional(v.id("users")) },
  handler: async (ctx, { partnerId }) => {
    const viewer = await requireUser(ctx, "any");
    
    let targetPartnerId = partnerId;
    if (viewer.role === "partner") {
      targetPartnerId = viewer._id;
    }
    
    // Handle sales users - they can only view summaries for assigned partners
    if (viewer.role === "sales") {
      const assignedPartnerIds = await getAssignedPartnerIds(ctx, viewer._id);
      
      if (!targetPartnerId) {
        throw new Error("Partner ID required for sales users");
      }
      
      if (!assignedPartnerIds.includes(targetPartnerId)) {
        throw new Error("Unauthorized: You can only view summaries for your assigned partners");
      }
    }
    
    if (!targetPartnerId) {
      throw new Error("Partner ID required");
    }
    
    const deals = await ctx.db
      .query("deals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .collect();
    
    const summary = {
      totalDeals: deals.length,
      inProgress: deals.filter(d => d.status === "in_progress").length,
      closed: deals.filter(d => d.status === "closed").length,
      lost: deals.filter(d => d.status === "lost").length,
      paid: deals.filter(d => d.status === "paid").length,
      totalValue: deals.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0),
      totalCommissionDue: deals.reduce((sum, d) => 
        sum + (d.commissionDueTokenUsd || 0) + (d.commissionDueFiatUsd || 0), 0),
      totalCommissionPending: deals.reduce((sum, d) => sum + (d.commissionPendingUsd || 0), 0),
    };
    
    return summary;
  }
});

// One-time fix for existing closed deals without commission values
export const fixExistingClosedDeals = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireUser(ctx, ["admin", "superadmin", "accounting"]);
    
    // Find all closed/paid deals without commission values
    const deals = await ctx.db.query("deals").collect();
    const dealsToFix = deals.filter(d => 
      (d.status === "closed" || d.status === "paid") && 
      d.dealValueUsd && 
      d.dealValueUsd > 0 && 
      (!d.commissionDueFiatUsd || d.commissionDueFiatUsd === 0)
    );
    
    console.log(`Found ${dealsToFix.length} deals to fix`);
    
    for (const deal of dealsToFix) {
      const partner = await ctx.db.get(deal.partnerId);
      if (!partner) continue;
      
      // Commission rates based on partner tier
      const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
        switch (tier) {
          case "diamond": return 10;
          case "elite": return 7.5;
          case "trusted": return 5;
          default: return 5;
        }
      };
      
      const rate = getCommissionRate(partner.tier);
      const commission = deal.dealValueUsd! * (rate / 100);
      
      await ctx.db.patch(deal._id, {
        commissionDueFiatUsd: commission,
        commissionPct: rate,
        lastUpdatedAt: Date.now(),
      });
      
      console.log(`Fixed deal ${deal.projectName}: $${commission} commission at ${rate}% for ${partner.name || partner.email}`);
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "BULK_FIX_COMMISSIONS",
      entity: "deals",
      meta: { dealsFixed: dealsToFix.length },
      at: Date.now(),
    });
    
    return { dealsFixed: dealsToFix.length };
  },
});

// Generate invoice for a deal
export const generateInvoice = mutation({
  args: {
    dealId: v.id("deals"),
    invoiceNotes: v.optional(v.string()),
    dueDate: v.optional(v.number()), // timestamp for due date
  },
  handler: async (ctx, { dealId, invoiceNotes, dueDate }) => {
    const user = await requireUser(ctx, ["partner", "sales", "ops", "accounting", "admin", "superadmin"]);
    
    const deal = await ctx.db.get(dealId);
    if (!deal) {
      throw new Error("Deal not found");
    }
    
    // Partners can only generate invoices for their own deals
    if (user.role === "partner" && deal.partnerId !== user._id) {
      throw new Error("Unauthorized: Can only generate invoices for your own deals");
    }
    
    // Can only generate invoices for closed or paid deals with commission
    if (!["closed", "paid"].includes(deal.status)) {
      throw new Error("Can only generate invoices for closed or paid deals");
    }
    
    const totalCommission = (deal.commissionDueFiatUsd || 0) + (deal.commissionDueTokenUsd || 0);
    if (totalCommission <= 0) {
      throw new Error("Cannot generate invoice: no commission amount found");
    }
    
    // Generate invoice number if not exists
    let invoiceNumber = deal.invoiceNumber;
    if (!invoiceNumber) {
      // Get partner's current invoice counter
      const partner = await ctx.db.get(deal.partnerId);
      if (!partner) {
        throw new Error("Partner not found");
      }
      
      // Use partner's next invoice number (starts at 1 for first invoice)
      const nextInvoiceNumber = (partner.lastInvoiceNumber || 0) + 1;
      invoiceNumber = nextInvoiceNumber.toString();
    }
    
    // Set default due date (30 days from now)
    const defaultDueDate = dueDate || (Date.now() + (30 * 24 * 60 * 60 * 1000));
    
    // Update deal with invoice information
    await ctx.db.patch(dealId, {
      invoiceNumber,
      invoiceStatus: "draft",
      invoiceGeneratedAt: Date.now(),
      invoiceDueDate: defaultDueDate,
      invoiceNotes: invoiceNotes || undefined,
      lastUpdatedAt: Date.now(),
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "INVOICE_GENERATED",
      entity: `deals/${dealId}`,
      meta: { invoiceNumber, totalCommission },
      at: Date.now(),
    });
    
    return { 
      invoiceNumber,
      dealId,
      totalCommission,
      dueDate: defaultDueDate
    };
  },
});

// Fix commission percentages for all deals
export const fixCommissionPercentages = mutation({
  args: {},
  handler: async (ctx) => {
    const user = await requireUser(ctx, ["admin", "superadmin", "accounting"]);
    
    // Commission rates based on partner tier
    const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
      switch (tier) {
        case "diamond": return 10;
        case "elite": return 7.5;
        case "trusted": return 5;
        default: return 5;
      }
    };
    
    // Find all deals with commission data
    const deals = await ctx.db.query("deals").collect();
    const dealsToFix = deals.filter(d => 
      d.commissionDueFiatUsd && d.commissionDueFiatUsd > 0
    );
    
    console.log(`Found ${dealsToFix.length} deals to check commission percentages`);
    
    let fixedCount = 0;
    for (const deal of dealsToFix) {
      const partner = await ctx.db.get(deal.partnerId);
      if (!partner) continue;
      
      const correctRate = getCommissionRate(partner.tier);
      
      // Only update if the commission percentage is different
      if (deal.commissionPct !== correctRate) {
        await ctx.db.patch(deal._id, {
          commissionPct: correctRate,
          lastUpdatedAt: Date.now(),
        });
        
        fixedCount++;
        console.log(`Fixed commission % for deal ${deal.projectName}: ${deal.commissionPct}% → ${correctRate}% for ${partner.tier || 'no tier'} partner`);
      }
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "BULK_FIX_COMMISSION_PERCENTAGES",
      entity: "deals",
      meta: { dealsChecked: dealsToFix.length, dealsFixed: fixedCount },
      at: Date.now(),
    });
    
    return { dealsChecked: dealsToFix.length, dealsFixed: fixedCount };
  },
});

// Get invoice data for PDF generation
export const getInvoiceData = query({
  args: { dealId: v.id("deals") },
  handler: async (ctx, { dealId }) => {
    const user = await requireUser(ctx, "any");
    
    const deal = await ctx.db.get(dealId);
    if (!deal) {
      throw new Error("Deal not found");
    }
    
    // Partners can only view their own deal invoices
    if (user.role === "partner" && deal.partnerId !== user._id) {
      throw new Error("Unauthorized: Can only view invoices for your own deals");
    }
    
    const partner = await ctx.db.get(deal.partnerId);
    if (!partner) {
      throw new Error("Partner not found");
    }
    
    // Always use current tier-based commission rate for display
    const getCommissionRate = (tier?: "trusted" | "elite" | "diamond"): number => {
      switch (tier) {
        case "diamond": return 10;
        case "elite": return 7.5;
        case "trusted": return 5;
        default: return 5;
      }
    };
    
    const currentCommissionRate = getCommissionRate(partner.tier);
    
    // IBC company details (recipient of the invoice)
    const ibcCompany = {
      name: "IBC VENTURES LTD",
      vatNumber: "*********",
      address: "Trust Company Complex Ajeltake Road, Ajeltake I, Ajeltake Road, Ajeltake Island, Majuro, Republic of the Marshall Islands MH 96960",
      phone: "+************",
      email: "<EMAIL>",
    };
    
    // Calculate totals
    const commissionFiat = deal.commissionDueFiatUsd || 0;
    const commissionToken = deal.commissionDueTokenUsd || 0;
    const subtotal = commissionFiat + commissionToken;
    const vatRate = 0; // Adjust based on jurisdiction
    const vatAmount = subtotal * (vatRate / 100);
    const total = subtotal + vatAmount;
    
    return {
      // Invoice metadata
      invoiceNumber: deal.invoiceNumber,
      invoiceDate: deal.invoiceGeneratedAt,
      dueDate: deal.invoiceDueDate,
      status: deal.invoiceStatus || "draft",
      notes: deal.invoiceNotes,
      
      // Company details (invoice sender - the partner)
      fromCompany: {
        name: partner.companyName || partner.fullName || partner.name || "Partner Company",
        address: partner.billingAddress || "Address not provided",
        phone: partner.phone || "Phone not provided",
        email: partner.email || "Email not provided",
        vatNumber: partner.companyType === "business" ? undefined : undefined, // Partners don't typically have VAT numbers we track
      },
      
      toCompany: ibcCompany,
      
      // Service details
      services: [
        {
          code: "BDS001",
          description: `Business Development Services - ${deal.projectName}`,
          quantity: 1,
          unitPrice: total,
          discount: 0,
          total: total,
          details: {
            dealType: deal.dealType,
            commissionRate: `${currentCommissionRate}%`,
            dealValue: deal.dealValueUsd ? `$${deal.dealValueUsd.toLocaleString()}` : "N/A",
            partnerTier: partner.tier ? partner.tier.charAt(0).toUpperCase() + partner.tier.slice(1) : "No Tier",
          }
        }
      ],
      
      // Financial totals
      financials: {
        subtotal,
        vatRate,
        vatAmount,
        total,
        commissionBreakdown: {
          fiatCommission: commissionFiat,
          tokenCommission: commissionToken,
        }
      },
      
      // Deal information
      deal: {
        id: deal._id,
        projectName: deal.projectName,
        dealType: deal.dealType,
        status: deal.status,
        dealValue: deal.dealValueUsd,
        commissionPct: deal.commissionPct,
      },
      
      // Partner information
      partner: {
        id: partner._id,
        name: partner.name || partner.email,
        companyName: partner.companyName,
        tier: partner.tier,
      },
    };
  },
});

// Update invoice status
export const updateInvoiceStatus = mutation({
  args: {
    dealId: v.id("deals"),
    status: v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    ),
  },
  handler: async (ctx, { dealId, status }) => {
    const user = await requireUser(ctx, ["partner", "sales", "ops", "accounting", "admin", "superadmin"]);
    
    const deal = await ctx.db.get(dealId);
    if (!deal) {
      throw new Error("Deal not found");
    }
    
    // Partners can only update status for their own deals
    if (user.role === "partner" && deal.partnerId !== user._id) {
      throw new Error("Unauthorized: Can only update invoice status for your own deals");
    }
    
    await ctx.db.patch(dealId, {
      invoiceStatus: status,
      lastUpdatedAt: Date.now(),
    });
    
    // Increment partner's invoice counter only when marking as paid
    if (status === "paid" && deal.invoiceStatus !== "paid") {
      const partner = await ctx.db.get(deal.partnerId);
      if (partner) {
        const currentCounter = partner.lastInvoiceNumber || 0;
        await ctx.db.patch(deal.partnerId, {
          lastInvoiceNumber: currentCounter + 1,
        });
      }
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "INVOICE_STATUS_UPDATED",
      entity: `deals/${dealId}`,
      meta: { newStatus: status, invoiceNumber: deal.invoiceNumber },
      at: Date.now(),
    });
    
    return { success: true };
  },
});

// List all invoices (deals with invoice numbers)
export const listInvoices = query({
  args: { 
    partnerId: v.optional(v.id("users")),
    status: v.optional(v.string())
  },
  handler: async (ctx, { partnerId, status }) => {
    const viewer = await requireUser(ctx, "any");
    
    // Determine which partner's invoices to show
    let targetPartnerId = partnerId;
    if (viewer.role === "partner") {
      targetPartnerId = viewer._id; // Partners can only see their own
    }
    
    // Handle sales users - they can only see invoices from their assigned partners
    if (viewer.role === "sales") {
      const assignedPartnerIds = await getAssignedPartnerIds(ctx, viewer._id);
      
      if (assignedPartnerIds.length === 0) {
        // Sales user has no assigned partners, return empty array
        return [];
      }
      
      if (targetPartnerId) {
        // Check if the requested partner is assigned to this sales user
        if (!assignedPartnerIds.includes(targetPartnerId)) {
          throw new Error("Unauthorized: You can only view invoices from your assigned partners");
        }
        
        const deals = await ctx.db
          .query("deals")
          .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
          .filter((q) => q.neq(q.field("invoiceNumber"), undefined))
          .collect();
        
        return status ? deals.filter(d => d.invoiceStatus === status) : deals;
      }
      
      // No specific partner requested, get invoices from all assigned partners
      const allInvoices = [];
      for (const partnerIdStr of assignedPartnerIds) {
        const partnerId = partnerIdStr as any; // Type assertion for Convex ID
        const partnerInvoices = await ctx.db
          .query("deals")
          .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
          .filter((q) => q.neq(q.field("invoiceNumber"), undefined))
          .collect();
        allInvoices.push(...partnerInvoices);
      }
      
      return status ? allInvoices.filter(d => d.invoiceStatus === status) : allInvoices;
    }
    
    if (!targetPartnerId) {
      // Internal users (admin, ops, accounting, superadmin) without specifying partnerId see all invoices
      const deals = await ctx.db
        .query("deals")
        .filter((q) => q.neq(q.field("invoiceNumber"), undefined))
        .collect();
      return status ? deals.filter(d => d.invoiceStatus === status) : deals;
    }
    
    const deals = await ctx.db
      .query("deals")
      .withIndex("by_partner", (q) => q.eq("partnerId", targetPartnerId))
      .filter((q) => q.neq(q.field("invoiceNumber"), undefined))
      .collect();
      
    return status ? deals.filter(d => d.invoiceStatus === status) : deals;
  }
});

// Get invoice by number
export const getInvoiceByNumber = query({
  args: { invoiceNumber: v.string() },
  handler: async (ctx, { invoiceNumber }) => {
    const user = await requireUser(ctx, "any");
    
    const deal = await ctx.db
      .query("deals")
      .withIndex("by_invoice_number", (q) => q.eq("invoiceNumber", invoiceNumber))
      .unique();
    
    if (!deal) {
      throw new Error("Invoice not found");
    }
    
    // Partners can only view their own invoices
    if (user.role === "partner" && deal.partnerId !== user._id) {
      throw new Error("Unauthorized: Can only view your own invoices");
    }
    
    return deal;
  },
});
