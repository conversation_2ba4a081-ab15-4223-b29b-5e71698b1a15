import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const addToWhitelist = mutation({
  args: {
    email: v.string(),
  },
  handler: async (ctx, { email }) => {
    const admin = await requireUser(ctx, ["admin", "superadmin"]);
    
    const existing = await ctx.db
      .query("whitelist")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();
      
    if (existing) {
      throw new Error("Email already whitelisted");
    }
    
    const whitelistId = await ctx.db.insert("whitelist", {
      email,
      invitedBy: admin._id,
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: "EMAIL_WHITELISTED",
      entity: `whitelist/${whitelistId}`,
      meta: { email },
      at: Date.now(),
    });
    
    return whitelistId;
  },
});

export const assignPartnerToSales = mutation({
  args: {
    partnerId: v.id("users"),
    salesUserId: v.id("users"),
  },
  handler: async (ctx, { partnerId, salesUserId }) => {
    const admin = await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    // Remove existing assignment
    const existing = await ctx.db
      .query("assignments")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .first();
      
    if (existing) {
      await ctx.db.delete(existing._id);
    }
    
    const assignmentId = await ctx.db.insert("assignments", {
      partnerId,
      salesUserId,
    });
    
    // Update existing leads
    const leads = await ctx.db
      .query("leads")
      .withIndex("by_partner", (q) => q.eq("partnerId", partnerId))
      .collect();
      
    for (const lead of leads) {
      await ctx.db.patch(lead._id, { salesUserId });
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: "PARTNER_ASSIGNED",
      entity: `assignments/${assignmentId}`,
      meta: { partnerId, salesUserId },
      at: Date.now(),
    });
    
    return assignmentId;
  },
});

export const approveUser = mutation({
  args: {
    userId: v.id("users"),
    approved: v.boolean(),
  },
  handler: async (ctx, { userId, approved }) => {
    const admin = await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    await ctx.db.patch(userId, { approved });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: approved ? "USER_APPROVED" : "USER_REJECTED",
      entity: `users/${userId}`,
      at: Date.now(),
    });
    
    return { ok: true };
  },
});

export const updateUserTier = mutation({
  args: {
    userId: v.id("users"),
    tier: v.union(v.literal("trusted"), v.literal("elite"), v.literal("diamond")),
  },
  handler: async (ctx, { userId, tier }) => {
    const admin = await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    await ctx.db.patch(userId, { tier });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: "USER_TIER_UPDATED",
      entity: `users/${userId}`,
      meta: { tier },
      at: Date.now(),
    });
    
    return { ok: true };
  },
});

export const makeAdmin = mutation({
  args: {
    userId: v.id("users"),
    role: v.union(v.literal("admin"), v.literal("superadmin"), v.literal("ops"), v.literal("accounting"), v.literal("sales")),
  },
  handler: async (ctx, { userId, role }) => {
    // Only superadmins can promote users to admin roles
    const admin = await requireUser(ctx, ["superadmin"]);
    
    const user = await ctx.db.get(userId);
    if (!user) throw new Error("User not found");
    
    await ctx.db.patch(userId, { role, approved: true });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: "USER_PROMOTED",
      entity: `users/${userId}`,
      meta: { role },
      at: Date.now(),
    });
    
    return { ok: true };
  },
});

export const listPendingApprovals = query({
  args: {},
  handler: async (ctx) => {
    await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    const pendingUsers = await ctx.db
      .query("users")
      .filter((q) => q.eq(q.field("approved"), false))
      .collect();
      
    const pendingLeads = await ctx.db
      .query("leads")
      .filter((q) => q.eq(q.field("approved"), undefined))
      .collect();
      
    const pendingWithdrawals = await ctx.db
      .query("withdrawals")
      .withIndex("by_status", (q) => q.eq("status", "in_review"))
      .collect();
    
    return {
      users: pendingUsers,
      leads: pendingLeads,
      withdrawals: pendingWithdrawals,
    };
  },
});

export const getAuditLogs = query({
  args: {
    limit: v.optional(v.number()),
    entity: v.optional(v.string()),
  },
  handler: async (ctx, { limit = 50, entity }) => {
    await requireUser(ctx, ["admin", "superadmin"]);
    
    let query = ctx.db.query("auditLogs").withIndex("by_time");
    
    const logs = await query.order("desc").take(limit);
    
    if (entity) {
      return logs.filter(log => log.entity.includes(entity));
    }
    
    return logs;
  },
});

export const listUsers = query({
  args: {},
  handler: async (ctx) => {
    const admin = await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    const users = await ctx.db.query("users").collect();
    return users.map(user => ({
      _id: user._id,
      email: user.email,
      name: user.name,
      role: user.role,
      tier: user.tier,
      approved: user.approved,
      _creationTime: user._creationTime,
    }));
  },
});

export const getUserById = query({
  args: { userId: v.id("users") },
  handler: async (ctx, { userId }) => {
    const user = await ctx.db.get(userId);
    return user;
  },
});

export const getUserByEmail = query({
  args: { email: v.string() },
  handler: async (ctx, { email }) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();
    return user;
  },
});

export const getAllUsers = query({
  args: {},
  handler: async (ctx) => {
    const users = await ctx.db.query("users").collect();
    return users.map(user => ({
      _id: user._id,
      email: user.email,
      name: user.name,
      role: user.role,
      tier: user.tier,
      approved: user.approved,
    }));
  },
});

export const makeFirstAdmin = mutation({
  args: { email: v.string() },
  handler: async (ctx, { email }) => {
    const user = await ctx.db
      .query("users")
      .withIndex("by_email", (q) => q.eq("email", email))
      .first();
    
    if (!user) throw new Error("User not found");
    
    await ctx.db.patch(user._id, { 
      role: "superadmin", 
      approved: true 
    });
    
    return { ok: true, userId: user._id };
  },
});

// Sales-specific functions
export const listAssignedPartners = query({
  args: {},
  handler: async (ctx) => {
    const sales = await requireUser(ctx, ["sales"]);
    
    const assignments = await ctx.db
      .query("assignments")
      .withIndex("by_sales", (q) => q.eq("salesUserId", sales._id))
      .collect();
    
    const partners = await Promise.all(
      assignments.map(async (assignment) => {
        const partner = await ctx.db.get(assignment.partnerId);
        return partner;
      })
    );
    
    return partners.filter(Boolean);
  },
});

// Ops-specific functions
export const getLeadStatistics = query({
  args: {},
  handler: async (ctx) => {
    await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    const leads = await ctx.db.query("leads").collect();
    
    return {
      total: leads.length,
      approved: leads.filter(l => l.approved === true).length,
      rejected: leads.filter(l => l.approved === false).length,
      pending: leads.filter(l => l.approved === undefined).length,
      byStatus: {
        warm: leads.filter(l => l.status === "warm").length,
        cold: leads.filter(l => l.status === "cold").length,
        won: leads.filter(l => l.status === "won").length,
        lost: leads.filter(l => l.status === "lost").length,
      }
    };
  },
});

// Accounting-specific functions
export const getFinancialSummary = query({
  args: {},
  handler: async (ctx) => {
    await requireUser(ctx, ["accounting", "admin", "superadmin"]);
    
    const deals = await ctx.db.query("deals").collect();
    const withdrawals = await ctx.db.query("withdrawals").collect();
    
    const totalDealValue = deals.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0);
    const totalCommissionsDue = deals.reduce((sum, d) => 
      sum + (d.commissionDueTokenUsd || 0) + (d.commissionDueFiatUsd || 0), 0);
    const totalCommissionsPending = deals.reduce((sum, d) => sum + (d.commissionPendingUsd || 0), 0);
    
    const totalWithdrawalRequests = withdrawals.reduce((sum, w) => sum + w.amountUsd, 0);
    const totalWithdrawalsPaid = withdrawals
      .filter(w => w.status === "paid")
      .reduce((sum, w) => sum + w.amountUsd, 0);
    const totalWithdrawalsPending = withdrawals
      .filter(w => w.status === "in_review")
      .reduce((sum, w) => sum + w.amountUsd, 0);
    
    return {
      deals: {
        totalValue: totalDealValue,
        totalCommissionsDue,
        totalCommissionsPending,
        count: deals.length,
      },
      withdrawals: {
        totalRequested: totalWithdrawalRequests,
        totalPaid: totalWithdrawalsPaid,
        totalPending: totalWithdrawalsPending,
        count: withdrawals.length,
      }
    };
  },
});
