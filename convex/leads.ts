import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const submit = mutation({
  args: {
    company: v.string(),
    website: v.optional(v.string()),
    twitter: v.optional(v.string()),
    pocName: v.optional(v.string()),
    pocRole: v.optional(v.string()),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const partner = await requireUser(ctx, ["partner"]);
    
    // Validate company name length
    if (args.company.length > 100) {
      throw new Error("Company name too long (max 100 characters)");
    }
    
    // Get assigned sales user
    const assignment = await ctx.db
      .query("assignments")
      .withIndex("by_partner", (q) => q.eq("partnerId", partner._id))
      .first();
    
    const leadId = await ctx.db.insert("leads", {
      partnerId: partner._id,
      salesUserId: assignment?.salesUserId,
      company: args.company,
      website: args.website,
      twitter: args.twitter,
      pocName: args.pocName,
      pocRole: args.pocRole,
      notes: args.notes,
      status: "warm",
      approved: undefined,
    });
    
    // Log the submission
    await ctx.db.insert("auditLogs", {
      actorUserId: partner._id,
      action: "LEAD_SUBMITTED",
      entity: `leads/${leadId}`,
      meta: { company: args.company },
      at: Date.now(),
    });
    
    return leadId;
  },
});

export const listMine = query({
  args: { status: v.optional(v.string()) },
  handler: async (ctx, { status }) => {
    const me = await requireUser(ctx, "any");
    
    if (me.role === "partner") {
      const leads = await ctx.db
        .query("leads")
        .withIndex("by_partner", (q) => q.eq("partnerId", me._id))
        .collect();
      return status ? leads.filter(l => l.status === status) : leads;
    }
    
    if (me.role === "sales") {
      const leads = await ctx.db
        .query("leads")
        .withIndex("by_sales", (q) => q.eq("salesUserId", me._id))
        .collect();
      return status ? leads.filter(l => l.status === status) : leads;
    }
    
    // ops/admin see all
    const leads = await ctx.db.query("leads").collect();
    return status ? leads.filter(l => l.status === status) : leads;
  }
});

export const approve = mutation({
  args: { 
    leadId: v.id("leads"), 
    approved: v.boolean(),
    telegramGroupUrl: v.optional(v.string()),
    // Deal creation parameters
    dealType: v.optional(v.string()),
    commissionPct: v.optional(v.number()),
  },
  handler: async (ctx, { leadId, approved, telegramGroupUrl, dealType, commissionPct }) => {
    const ops = await requireUser(ctx, ["ops", "admin", "superadmin"]);
    
    const lead = await ctx.db.get(leadId);
    if (!lead) {
      throw new Error("Lead not found");
    }
    
    // Update the lead
    await ctx.db.patch(leadId, { 
      approved,
      telegramGroupUrl,
    });
    
    // If approved, automatically create a deal
    if (approved) {
      // Get partner info for tier-based commission
      const partner = await ctx.db.get(lead.partnerId);
      if (!partner) {
        throw new Error("Partner not found");
      }
      
      // Determine commission percentage based on tier if not provided
      let finalCommissionPct = commissionPct;
      if (!finalCommissionPct) {
        switch (partner.tier) {
          case "elite":
            finalCommissionPct = 7.5;
            break;
          case "diamond":
            finalCommissionPct = 10;
            break;
          default:
            finalCommissionPct = 5; // trusted tier
        }
      }
      
      // Create the deal
      const dealId = await ctx.db.insert("deals", {
        leadId: leadId,
        partnerId: lead.partnerId,
        salesUserId: lead.salesUserId,
        projectName: lead.company,
        dealType: dealType || "Standard Partnership",
        status: "in_progress",
        commissionPct: finalCommissionPct,
        lastUpdatedAt: Date.now(),
      });
      
      // Log deal creation
      await ctx.db.insert("auditLogs", {
        actorUserId: ops._id,
        action: "DEAL_AUTO_CREATED",
        entity: `deals/${dealId}`,
        meta: { 
          leadId: leadId,
          projectName: lead.company,
          commissionPct: finalCommissionPct,
          autoGenerated: true
        },
        at: Date.now(),
      });
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: ops._id,
      action: approved ? "LEAD_APPROVED" : "LEAD_REJECTED",
      entity: `leads/${leadId}`,
      meta: { telegramGroupUrl, autoGeneratedDeal: approved },
      at: Date.now(),
    });
    
    return { ok: true };
  }
});

export const updateStatus = mutation({
  args: {
    leadId: v.id("leads"),
    status: v.union(v.literal("warm"), v.literal("cold"), v.literal("won"), v.literal("lost")),
  },
  handler: async (ctx, { leadId, status }) => {
    const user = await requireUser(ctx, ["sales", "ops", "admin", "superadmin"]);
    
    await ctx.db.patch(leadId, { status });
    
    // If lead status is updated to "won", update the corresponding deal status
    if (status === "won") {
      const deal = await ctx.db
        .query("deals")
        .filter((q) => q.eq(q.field("leadId"), leadId))
        .first();
        
      if (deal && deal.status === "in_progress") {
        await ctx.db.patch(deal._id, { 
          status: "closed",
          lastUpdatedAt: Date.now(),
        });
        
        await ctx.db.insert("auditLogs", {
          actorUserId: user._id,
          action: "DEAL_AUTO_CLOSED",
          entity: `deals/${deal._id}`,
          meta: { reason: "Lead marked as won", leadId },
          at: Date.now(),
        });
      }
    }
    
    await ctx.db.insert("auditLogs", {
      actorUserId: user._id,
      action: "LEAD_STATUS_UPDATED",
      entity: `leads/${leadId}`,
      meta: { status },
      at: Date.now(),
    });
    
    return { ok: true };
  }
});
