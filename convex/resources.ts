import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const create = mutation({
  args: {
    title: v.string(),
    category: v.optional(v.string()),
    storageId: v.id("_storage"),
    audience: v.union(
      v.literal("all"),
      v.literal("trusted"),
      v.literal("elite"),
      v.literal("diamond"),
      v.literal("internal")
    ),
  },
  handler: async (ctx, args) => {
    const admin = await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    const resourceId = await ctx.db.insert("resources", args);
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: "RESOURCE_CREATED",
      entity: `resources/${resourceId}`,
      meta: { title: args.title, audience: args.audience },
      at: Date.now(),
    });
    
    return resourceId;
  },
});

export const list = query({
  args: {},
  handler: async (ctx) => {
    const user = await requireUser(ctx, "any");
    
    // Determine what resources the user can see based on their tier/role
    let allowedAudiences: string[] = ["all"];
    
    if (user.role === "partner") {
      if (user.tier === "trusted") {
        allowedAudiences.push("trusted");
      } else if (user.tier === "elite") {
        allowedAudiences.push("trusted", "elite");
      } else if (user.tier === "diamond") {
        allowedAudiences.push("trusted", "elite", "diamond");
      }
    } else {
      // Internal users can see all resources including internal ones
      allowedAudiences.push("trusted", "elite", "diamond", "internal");
    }
    
    const resources = await ctx.db.query("resources").collect();
    
    const filteredResources = resources.filter(resource => 
      allowedAudiences.includes(resource.audience)
    );
    
    // Get signed URLs for each resource
    const resourcesWithUrls = await Promise.all(
      filteredResources.map(async (resource) => ({
        ...resource,
        url: await ctx.storage.getUrl(resource.storageId),
      }))
    );
    
    return resourcesWithUrls;
  },
});

export const generateUploadUrl = mutation({
  args: {},
  handler: async (ctx) => {
    await requireUser(ctx, ["admin", "superadmin", "ops"]);
    return await ctx.storage.generateUploadUrl();
  },
});

export const deleteResource = mutation({
  args: { resourceId: v.id("resources") },
  handler: async (ctx, { resourceId }) => {
    const admin = await requireUser(ctx, ["admin", "superadmin", "ops"]);
    
    const resource = await ctx.db.get(resourceId);
    if (!resource) {
      throw new Error("Resource not found");
    }
    
    await ctx.db.delete(resourceId);
    
    await ctx.db.insert("auditLogs", {
      actorUserId: admin._id,
      action: "RESOURCE_DELETED",
      entity: `resources/${resourceId}`,
      meta: { title: resource.title },
      at: Date.now(),
    });
    
    return { ok: true };
  },
});
