import { defineSchema, defineTable } from "convex/server";
import { v } from "convex/values";

export default defineSchema({
  assignments: defineTable({
    partnerId: v.id("users"),
    salesUserId: v.id("users"),
  })
    .index("by_partner", ["partnerId"])
    .index("by_sales", ["salesUserId"]),
  auditLogs: defineTable({
    action: v.string(),
    actorUserId: v.id("users"),
    at: v.float64(),
    entity: v.string(),
    meta: v.optional(v.any()),
  }).index("by_time", ["at"]),
  authAccounts: defineTable({
    emailVerified: v.optional(v.string()),
    phoneVerified: v.optional(v.string()),
    provider: v.string(),
    providerAccountId: v.string(),
    secret: v.optional(v.string()),
    userId: v.id("users"),
  })
    .index("providerAndAccountId", [
      "provider",
      "providerAccountId",
    ])
    .index("userIdAndProvider", ["userId", "provider"]),
  authRateLimits: defineTable({
    attemptsLeft: v.float64(),
    identifier: v.string(),
    lastAttemptTime: v.float64(),
  }).index("identifier", ["identifier"]),
  authRefreshTokens: defineTable({
    expirationTime: v.float64(),
    firstUsedTime: v.optional(v.float64()),
    parentRefreshTokenId: v.optional(
      v.id("authRefreshTokens")
    ),
    sessionId: v.id("authSessions"),
  })
    .index("sessionId", ["sessionId"])
    .index("sessionIdAndParentRefreshTokenId", [
      "sessionId",
      "parentRefreshTokenId",
    ]),
  authSessions: defineTable({
    expirationTime: v.float64(),
    userId: v.id("users"),
  }).index("userId", ["userId"]),
  authVerificationCodes: defineTable({
    accountId: v.id("authAccounts"),
    code: v.string(),
    emailVerified: v.optional(v.string()),
    expirationTime: v.float64(),
    phoneVerified: v.optional(v.string()),
    provider: v.string(),
    verifier: v.optional(v.string()),
  })
    .index("accountId", ["accountId"])
    .index("code", ["code"]),
  authVerifiers: defineTable({
    sessionId: v.optional(v.id("authSessions")),
    signature: v.optional(v.string()),
  }).index("signature", ["signature"]),
  deals: defineTable({
    averageSellingPrice: v.optional(v.float64()),
    closedBy: v.optional(v.string()),
    commissionDueFiatUsd: v.optional(v.float64()),
    commissionDueTokenUsd: v.optional(v.float64()),
    commissionPct: v.float64(),
    commissionPendingUsd: v.optional(v.float64()),
    dealType: v.string(),
    dealValueUsd: v.optional(v.float64()),
    invoiceDueDate: v.optional(v.float64()),
    invoiceGeneratedAt: v.optional(v.float64()),
    invoiceNotes: v.optional(v.string()),
    invoiceNumber: v.optional(v.string()),
    invoiceStatus: v.optional(v.union(
      v.literal("draft"),
      v.literal("sent"),
      v.literal("paid"),
      v.literal("overdue"),
      v.literal("cancelled")
    )),
    invoiceStorageId: v.optional(v.id("_storage")),
    lastUpdatedAt: v.float64(),
    launched: v.optional(v.boolean()),
    leadId: v.optional(v.id("leads")),
    liquidatedTokens: v.optional(v.float64()),
    liquidationUsd: v.optional(v.float64()),
    month: v.optional(v.number()),
    paidFiatUsd: v.optional(v.float64()),
    partnerId: v.id("users"),
    projectName: v.string(),
    receivedTokens: v.optional(v.float64()),
    remainingFiatUsd: v.optional(v.float64()),
    salesUserId: v.optional(v.id("users")),
    source: v.optional(v.string()),
    status: v.union(
      v.literal("in_progress"),
      v.literal("closed"),
      v.literal("lost"),
      v.literal("paid")
    ),
    totalTokens: v.optional(v.float64()),
    year: v.optional(v.number()),
  })
    .index("by_lead", ["leadId"])
    .index("by_partner", ["partnerId"])
    .index("by_partner_status", ["partnerId", "status"])
    .index("by_sales", ["salesUserId"])
    .index("by_value", ["dealValueUsd"])
    .index("by_invoice_number", ["invoiceNumber"])
    .index("by_invoice_status", ["invoiceStatus"]),
  leads: defineTable({
    approved: v.optional(v.boolean()),
    company: v.string(),
    notes: v.optional(v.string()),
    partnerId: v.id("users"),
    pocName: v.optional(v.string()),
    pocRole: v.optional(v.string()),
    referralLinkId: v.optional(v.id("referralLinks")),
    salesUserId: v.optional(v.id("users")),
    status: v.union(
      v.literal("warm"),
      v.literal("cold"),
      v.literal("won"),
      v.literal("lost")
    ),
    telegramGroupUrl: v.optional(v.string()),
    twitter: v.optional(v.string()),
    website: v.optional(v.string()),
  })
    .index("by_partner", ["partnerId"])
    .index("by_referral", ["referralLinkId"])
    .index("by_sales", ["salesUserId"])
    .index("by_status", ["status"]),
  messages: defineTable({
    body: v.string(),
    fromUserId: v.id("users"),
    kind: v.union(
      v.literal("feedback"),
      v.literal("support")
    ),
    resolved: v.boolean(),
    toUserId: v.optional(v.id("users")),
  })
    .index("by_from", ["fromUserId"])
    .index("by_to", ["toUserId"]),
  referralClicks: defineTable({
    ip: v.optional(v.string()),
    linkId: v.id("referralLinks"),
    userAgent: v.optional(v.string()),
    utm: v.optional(
      v.object({
        campaign: v.optional(v.string()),
        medium: v.optional(v.string()),
        source: v.optional(v.string()),
      })
    ),
  }).index("by_link", ["linkId"]),
  referralLinks: defineTable({
    code: v.string(),
    destinationUrl: v.string(),
    ownerId: v.id("users"),
    title: v.optional(v.string()),
  })
    .index("by_code", ["code"])
    .index("by_owner", ["ownerId"]),
  resources: defineTable({
    audience: v.union(
      v.literal("all"),
      v.literal("trusted"),
      v.literal("elite"),
      v.literal("diamond"),
      v.literal("internal")
    ),
    category: v.optional(v.string()),
    storageId: v.id("_storage"),
    title: v.string(),
  }).index("by_audience", ["audience"]),
  termsAcceptances: defineTable({
    acceptedAt: v.float64(),
    userId: v.id("users"),
    version: v.string(),
  })
    .index("by_user", ["userId"])
    .index("by_version", ["version"]),
  termsVersions: defineTable({
    bodyStorageId: v.id("_storage"),
    publishedAt: v.float64(),
    title: v.string(),
    version: v.string(),
  }).index("by_version", ["version"]),
  users: defineTable({
    approved: v.optional(v.boolean()),
    billingAddress: v.optional(v.string()),
    clerkId: v.optional(v.string()),
    companyName: v.optional(v.string()),
    companyType: v.optional(v.string()),
    companyTypeOther: v.optional(v.string()),
    defaultReferralCode: v.optional(v.string()),
    email: v.optional(v.string()),
    emailDetails: v.optional(v.string()),
    emailVerificationTime: v.optional(v.float64()),
    fullName: v.optional(v.string()),
    idDocStorageId: v.optional(v.id("_storage")),
    image: v.optional(v.string()),
    internalPocId: v.optional(v.id("users")),
    name: v.optional(v.string()),
    phone: v.optional(v.string()),
    phoneVerificationTime: v.optional(v.float64()),
    preferredCommunication: v.optional(
      v.array(
        v.union(
          v.literal("whatsapp"),
          v.literal("email"),
          v.literal("telegram")
        )
      )
    ),
    preferredPaymentMethod: v.optional(
      v.union(v.literal("bank"), v.literal("usdt"))
    ),
    profileCompleted: v.optional(v.boolean()),
    role: v.optional(
      v.union(
        v.literal("partner"),
        v.literal("sales"),
        v.literal("ops"),
        v.literal("accounting"),
        v.literal("admin"),
        v.literal("superadmin")
      )
    ),
    roleTitle: v.optional(v.string()),
    roleTitleOther: v.optional(v.string()),
    roles: v.optional(
      v.array(
        v.union(
          v.literal("partner"),
          v.literal("sales"),
          v.literal("ops"),
          v.literal("accounting"),
          v.literal("admin"),
          v.literal("superadmin")
        )
      )
    ),
    softDeletedAt: v.optional(v.float64()),
    telegram: v.optional(v.string()),
    termsAcceptedAt: v.optional(v.float64()),
    termsAcceptedVersion: v.optional(v.string()),
    tier: v.optional(
      v.union(
        v.literal("trusted"),
        v.literal("elite"),
        v.literal("diamond")
      )
    ),
    whatsapp: v.optional(v.string()),
    xProfile: v.optional(v.string()),
    lastInvoiceNumber: v.optional(v.number()),
  })
    .index("by_clerkId", ["clerkId"])
    .index("by_email", ["email"])
    .index("by_role", ["role"])
    .index("by_tier", ["tier"]),
  whitelist: defineTable({
    email: v.string(),
    invitedBy: v.optional(v.id("users")),
  }).index("by_email", ["email"]),
  withdrawals: defineTable({
    amountUsd: v.float64(),
    bankDetails: v.optional(v.string()),
    invoiceStorageId: v.optional(v.id("_storage")),
    method: v.union(v.literal("usdt"), v.literal("bank")),
    partnerId: v.id("users"),
    status: v.union(
      v.literal("in_review"),
      v.literal("approved"),
      v.literal("paid"),
      v.literal("rejected")
    ),
    txIdOrRef: v.optional(v.string()),
    walletAddress: v.optional(v.string()),
  })
    .index("by_partner", ["partnerId"])
    .index("by_status", ["status"]),
});