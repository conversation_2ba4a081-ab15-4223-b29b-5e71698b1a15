import { query } from "./_generated/server";
import { v } from "convex/values";

export const loggedInUser = query({
  args: {},
  returns: v.union(v.null(), v.any()), // Returns user document or null
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      return null;
    }
    
    // Find user by Clerk subject (user ID)
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerkId", (q) => q.eq("clerkId", identity.subject))
      .first();
      
    return user;
  },
});