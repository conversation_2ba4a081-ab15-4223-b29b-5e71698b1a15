import { mutation, query } from "./_generated/server";
import { v } from "convex/values";
import { requireUser } from "./lib/authz";

export const createLink = mutation({
  args: {
    code: v.string(),
    destinationUrl: v.string(),
    title: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    const partner = await requireUser(ctx, ["partner"]);
    
    // Check if code already exists
    const existing = await ctx.db
      .query("referralLinks")
      .withIndex("by_code", (q) => q.eq("code", args.code))
      .first();
      
    if (existing) {
      throw new Error("Referral code already exists");
    }
    
    const linkId = await ctx.db.insert("referralLinks", {
      ownerId: partner._id,
      code: args.code,
      destinationUrl: args.destinationUrl,
      title: args.title,
    });
    
    await ctx.db.insert("auditLogs", {
      actorUserId: partner._id,
      action: "REFERRAL_LINK_CREATED",
      entity: `referralLinks/${linkId}`,
      meta: { code: args.code },
      at: Date.now(),
    });
    
    return linkId;
  },
});

export const listMine = query({
  args: {},
  handler: async (ctx) => {
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) throw new Error("Not authenticated");
    
    // Find user by Clerk ID
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerkId", (q) => q.eq("clerkId", identity.subject))
      .first();
      
    if (!user) throw new Error("User not found");
    if (!user.approved) return []; // Return empty array for unapproved users
    
    // Allow partners or users without a role set (default to partner)
    if (user.role && user.role !== "partner") {
      return []; // Return empty array for non-partners instead of throwing error
    }
    
    const links = await ctx.db
      .query("referralLinks")
      .withIndex("by_owner", (q) => q.eq("ownerId", user._id))
      .collect();
    
    // Get click counts for each link
    const linksWithStats = await Promise.all(
      links.map(async (link) => {
        const clicks = await ctx.db
          .query("referralClicks")
          .withIndex("by_link", (q) => q.eq("linkId", link._id))
          .collect();
          
        const leads = await ctx.db
          .query("leads")
          .filter((q) => q.eq(q.field("referralLinkId"), link._id))
          .collect();
        
        return {
          ...link,
          clickCount: clicks.length,
          leadCount: leads.length,
        };
      })
    );
    
    return linksWithStats;
  },
});

export const trackClick = mutation({
  args: {
    code: v.string(),
    ip: v.optional(v.string()),
    userAgent: v.optional(v.string()),
    utm: v.optional(v.object({
      source: v.optional(v.string()),
      medium: v.optional(v.string()),
      campaign: v.optional(v.string()),
    })),
  },
  handler: async (ctx, args) => {
    const link = await ctx.db
      .query("referralLinks")
      .withIndex("by_code", (q) => q.eq("code", args.code))
      .first();
      
    if (!link) {
      throw new Error("Referral link not found");
    }
    
    const clickId = await ctx.db.insert("referralClicks", {
      linkId: link._id,
      ip: args.ip,
      userAgent: args.userAgent,
      utm: args.utm,
    });
    
    return { linkId: link._id, clickId };
  },
});

export const submitLeadFromReferral = mutation({
  args: {
    referralCode: v.string(),
    company: v.string(),
    website: v.optional(v.string()),
    twitter: v.optional(v.string()),
    pocName: v.string(),
    pocRole: v.string(),
    notes: v.optional(v.string()),
  },
  handler: async (ctx, args) => {
    // Find the referral link
    const referralLink = await ctx.db
      .query("referralLinks")
      .withIndex("by_code", (q) => q.eq("code", args.referralCode))
      .first();
      
    if (!referralLink) {
      throw new Error("Invalid referral code");
    }
    
    // Get the partner who owns this referral link
    const partner = await ctx.db.get(referralLink.ownerId);
    if (!partner) {
      throw new Error("Partner not found");
    }
    
    // Get assigned sales user for this partner
    const assignment = await ctx.db
      .query("assignments")
      .withIndex("by_partner", (q) => q.eq("partnerId", partner._id))
      .first();
    
    // Create the lead
    const leadId = await ctx.db.insert("leads", {
      partnerId: partner._id,
      salesUserId: assignment?.salesUserId,
      referralLinkId: referralLink._id,
      company: args.company,
      website: args.website,
      twitter: args.twitter,
      pocName: args.pocName,
      pocRole: args.pocRole,
      notes: args.notes,
      status: "warm",
      approved: undefined,
    });
    
    // Log the submission
    await ctx.db.insert("auditLogs", {
      actorUserId: partner._id,
      action: "LEAD_SUBMITTED_FROM_REFERRAL",
      entity: `leads/${leadId}`,
      meta: { 
        company: args.company,
        referralCode: args.referralCode,
        pocName: args.pocName 
      },
      at: Date.now(),
    });
    
    return leadId;
  },
});

export const getLinkByCode = query({
  args: { code: v.string() },
  handler: async (ctx, { code }) => {
    const link = await ctx.db
      .query("referralLinks")
      .withIndex("by_code", (q) => q.eq("code", code))
      .first();
      
    if (!link) {
      return null;
    }
    
    const owner = await ctx.db.get(link.ownerId);
    
    return {
      ...link,
      ownerName: owner?.name || owner?.email || "Unknown",
      ownerCompany: owner?.companyName,
    };
  },
});
