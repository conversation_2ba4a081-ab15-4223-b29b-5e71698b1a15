import { useQuery } from "convex/react";
import { useState } from "react";
import { api } from "../../convex/_generated/api";

import { SuperAdminPanel } from "./admin/panels/SuperAdminPanel";
import { AdminPanel as AdminPanelComponent } from "./admin/panels/AdminPanel";
import { OpsPanel } from "./admin/panels/OpsPanel";
import { AccountingPanel } from "./admin/panels/AccountingPanel";
import { NavigationTabs } from "./admin/shared/NavigationTabs";

export function AdminPanel() {
  const user = useQuery(api.users.myProfile);
  const [superAdminActiveTab, setSuperAdminActiveTab] = useState<
    "superadmin" | "admin" | "ops" | "accounting"
  >("superadmin");

  if (!user) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  // Helper function to get user roles
  const getUserRoles = (user: any) => {
    if (user.roles && user.roles.length > 0) {
      return user.roles;
    }
    if (user.role) {
      return [user.role];
    }
    return ["partner"];
  };

  const userRoles = getUserRoles(user);
  const hasRole = (role: string) => userRoles.includes(role);

  // Determine which panel to show based on highest priority role
  if (hasRole("superadmin")) {
    const tabs = [
      { id: "superadmin", label: "Super Admin" },
      { id: "admin", label: "Admin" },
      { id: "ops", label: "Operations" },
      { id: "accounting", label: "Accounting" },
    ];

    return (
      <div className="space-y-grid-6">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-grid-6">
          <NavigationTabs
            tabs={tabs}
            activeTab={superAdminActiveTab}
            onTabChange={(id) =>
              setSuperAdminActiveTab(id as "superadmin" | "admin" | "ops" | "accounting")
            }
          />
        </div>

        {superAdminActiveTab === "superadmin" && <SuperAdminPanel />}
        {superAdminActiveTab === "admin" && <AdminPanelComponent />}
        {superAdminActiveTab === "ops" && <OpsPanel />}
        {superAdminActiveTab === "accounting" && <AccountingPanel />}
      </div>
    );
  } else if (hasRole("admin")) {
    return <AdminPanelComponent />;
  } else if (hasRole("ops")) {
    return <OpsPanel />;
  } else if (hasRole("accounting")) {
    return <AccountingPanel />;
  } else {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-xl font-semibold mb-4">Access Denied</h2>
          <p className="text-gray-600">You don't have permission to access admin features.</p>
          <p className="text-sm text-gray-500 mt-2">
            Available roles: superadmin, admin, ops, accounting
          </p>
        </div>
      </div>
    );
  }
}
