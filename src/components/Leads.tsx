import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState } from "react";
import { toast } from "sonner";

export function Leads() {
  const user = useQuery(api.users.myProfile);
  const leads = useQuery(api.leads.listMine, {});
  const submitLead = useMutation(api.leads.submit);
  const approveLead = useMutation(api.leads.approve);
  const updateStatus = useMutation(api.leads.updateStatus);
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    company: "",
    website: "",
    twitter: "",
    pocName: "",
    pocRole: "",
    notes: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    console.debug("[Leads] handleSubmit:start", { formData });
    if (!formData.company) {
      console.warn("[Leads] handleSubmit:missing_company");
      toast.error("Company name is required");
      return;
    }

    try {
      setIsSubmitting(true);
      console.debug("[Leads] handleSubmit:submitting");
      await submitLead(formData);
      console.debug("[Leads] handleSubmit:success");
      toast.success("Lead submitted successfully");
      setFormData({
        company: "",
        website: "",
        twitter: "",
        pocName: "",
        pocRole: "",
        notes: "",
      });
    } catch (error) {
      console.error("[Leads] handleSubmit:error", error);
      toast.error(error instanceof Error ? error.message : "Failed to submit lead");
    } finally {
      setIsSubmitting(false);
      console.debug("[Leads] handleSubmit:finished");
    }
  };

  const handleApprove = async (leadId: Id<"leads">, approved: boolean, telegramGroupUrl?: string) => {
    console.debug("[Leads] handleApprove:start", { leadId, approved, telegramGroupUrl });
    try {
      await approveLead({ leadId, approved, telegramGroupUrl });
      console.debug("[Leads] handleApprove:success", { leadId, approved });
      toast.success(`Lead ${approved ? "approved" : "rejected"}`);
    } catch (error) {
      console.error("[Leads] handleApprove:error", error);
      toast.error(error instanceof Error ? error.message : "Failed to update lead");
    }
  };

  const handleStatusUpdate = async (leadId: Id<"leads">, status: "warm" | "cold" | "won" | "lost") => {
    console.debug("[Leads] handleStatusUpdate:start", { leadId, status });
    try {
      await updateStatus({ leadId, status });
      console.debug("[Leads] handleStatusUpdate:success", { leadId, status });
      toast.success("Lead status updated");
    } catch (error) {
      console.error("[Leads] handleStatusUpdate:error", error);
      toast.error(error instanceof Error ? error.message : "Failed to update status");
    }
  };

  if (!user || leads === undefined) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-accent border-t-transparent"></div>
          <p className="text-size-4 text-paragraph">Loading leads...</p>
        </div>
      </div>
    );
  }

  const canSubmitLeads = user.role === "partner";
  const canApproveLeads = user.role ? ["ops", "admin", "superadmin"].includes(user.role) : false;
  const canUpdateStatus = user.role ? ["sales", "ops", "admin", "superadmin"].includes(user.role) : false;

  return (
    <div className="space-y-grid-8 animate-fade-in">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-grid-4">
        <div className="space-y-grid-2">
          <h1 className="text-size-1">Leads</h1>
          <p className="text-size-3 text-paragraph">Manage your lead submissions and track their progress</p>
        </div>
        {canSubmitLeads && (
          <button
            onClick={() => setIsSubmitting(!isSubmitting)}
            className={`btn-gradient hover-lift ${isSubmitting ? 'bg-destructive hover:bg-destructive' : ''}`}
          >
            {isSubmitting ? "✕ Cancel" : "➕ Submit New Lead"}
          </button>
        )}
      </div>

      {canSubmitLeads && (
        <div className="glass-card hover-lift animate-slide-up">
          <div className="card-content">
            <div className="flex items-center gap-grid-3 mb-grid-4">
              <div className="w-10 h-10 bg-accent/20 rounded-lg flex items-center justify-center">
                <span className="text-accent text-size-2">💡</span>
              </div>
              <h2 className="text-size-2">Lead Submission Options</h2>
            </div>
            <div className="space-y-grid-3 text-size-3 text-paragraph">
              <div className="flex items-start gap-grid-3">
                <span className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <p><span className="text-foreground font-semibold">Manual Submission:</span> Use the form below to submit leads directly</p>
              </div>
              <div className="flex items-start gap-grid-3">
                <span className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <p><span className="text-foreground font-semibold">Referral Links:</span> Share your referral links so prospects can submit their own information</p>
              </div>
              <div className="flex items-start gap-grid-3">
                <span className="w-2 h-2 bg-accent rounded-full mt-2 flex-shrink-0"></span>
                <p>When someone uses your referral link, their lead will automatically be assigned to you</p>
              </div>
            </div>
          </div>
        </div>
      )}

      {canSubmitLeads && isSubmitting && (
        <div className="glass-card animate-scale-in">
          <div className="card-content">
            <div className="flex items-center gap-grid-3 mb-grid-6">
              <div className="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
                <span className="text-accent-foreground text-size-2">📝</span>
              </div>
              <h2 className="text-size-2">Submit New Lead</h2>
            </div>
            <form onSubmit={handleSubmit} className="space-y-grid-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-6">
                <div className="form-floating">
                  <input
                    type="text"
                    value={formData.company}
                    onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                    placeholder=" "
                    required
                    className="hover-glow"
                  />
                  <label>Company Name *</label>
                </div>
                <div className="form-floating">
                  <input
                    type="url"
                    value={formData.website}
                    onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                    placeholder=" "
                  />
                  <label>Website</label>
                </div>
                <div className="form-floating">
                  <input
                    type="text"
                    value={formData.twitter}
                    onChange={(e) => setFormData({ ...formData, twitter: e.target.value })}
                    placeholder=" "
                  />
                  <label>Twitter/X Handle</label>
                </div>
                <div className="form-floating">
                  <input
                    type="text"
                    value={formData.pocName}
                    onChange={(e) => setFormData({ ...formData, pocName: e.target.value })}
                    placeholder=" "
                  />
                  <label>Point of Contact Name</label>
                </div>
                <div className="form-floating">
                  <input
                    type="text"
                    value={formData.pocRole}
                    onChange={(e) => setFormData({ ...formData, pocRole: e.target.value })}
                    placeholder=" "
                  />
                  <label>POC Role/Title</label>
                </div>
              </div>
              <div className="form-floating">
                <textarea
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  rows={3}
                  placeholder=" "
                  className="auth-input-field resize-none"
                />
                <label>Additional Notes</label>
              </div>
              <div className="flex gap-grid-4 pt-grid-4">
                <button
                  type="submit"
                  disabled={isSubmitting}
                  className="btn-gradient hover-lift disabled:opacity-50 disabled:cursor-not-allowed flex-1 sm:flex-none"
                >
                  ✨ Submit Lead
                </button>
                <button
                  type="button"
                  onClick={() => setIsSubmitting(false)}
                  className="btn-secondary hover-lift"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="space-y-grid-6">
        {leads.length === 0 ? (
          <div className="glass-card">
            <div className="card-content text-center py-grid-8">
              <div className="w-16 h-16 bg-muted/20 rounded-full flex items-center justify-center mx-auto mb-grid-6">
                <span className="text-size-1">📋</span>
              </div>
              <h3 className="text-size-2 mb-grid-2">No leads found</h3>
              <p className="text-size-3 max-w-md mx-auto text-paragraph">
                {canSubmitLeads ? "Get started by submitting your first lead or sharing your referral links with potential clients." : "Your submitted leads will appear here once they're processed."}
              </p>
              {canSubmitLeads && !isSubmitting && (
                <button
                  onClick={() => setIsSubmitting(true)}
                  className="btn-gradient hover-lift mt-grid-6"
                >
                  ✨ Submit Your First Lead
                </button>
              )}
            </div>
          </div>
        ) : (
          leads.map((lead, index) => (
            <div 
              key={lead._id} 
              className="glass-card hover-lift animate-slide-up"
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              <div className="card-content">
                <div className="flex flex-col gap-grid-6">
                  {/* Header with company and status badges */}
                  <div className="flex flex-col sm:flex-row sm:items-start sm:justify-between gap-grid-4">
                    <div className="flex items-center gap-grid-3 flex-1 min-w-0">
                      <div className="w-10 h-10 bg-gradient-surface rounded-lg flex items-center justify-center flex-shrink-0">
                        <span className="text-foreground text-size-2">🏢</span>
                      </div>
                      <div className="min-w-0 flex-1">
                        <h3 className="text-size-2 truncate">{lead.company}</h3>
                        <p className="text-size-4 text-paragraph">
                          {new Date(lead._creationTime).toLocaleDateString()} 
                          {lead.referralLinkId && " • Via referral link"}
                        </p>
                      </div>
                    </div>
                    
                    {/* Status badges - stacked on mobile, side by side on larger screens */}
                    <div className="flex flex-wrap gap-grid-2 sm:flex-col sm:items-end">
                      <span className={`status-badge-compact ${
                        lead.status === "won" ? "status-success" :
                        lead.status === "warm" ? "status-warning" :
                        lead.status === "cold" ? "status-info" :
                        "status-error"
                      }`}>
                        <span>{lead.status === "won" ? "🏆" : lead.status === "warm" ? "🔥" : lead.status === "cold" ? "❄️" : "💔"}</span>
                        <span>{lead.status === "won" ? "Won" : lead.status === "warm" ? "Warm" : lead.status === "cold" ? "Cold" : "Lost"}</span>
                      </span>
                      {lead.approved !== undefined && (
                        <span className={`status-badge-compact ${
                          lead.approved ? "status-success" : "status-error"
                        }`}>
                          <span>{lead.approved ? "✅" : "❌"}</span>
                          <span>{lead.approved ? "Approved" : "Rejected"}</span>
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Contact details */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-grid-4">
                    {lead.website && (
                      <div className="flex items-center gap-grid-2 min-w-0">
                        <span className="text-accent flex-shrink-0">🌐</span>
                        <a 
                          href={lead.website} 
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-size-4 text-accent hover:text-foreground transition-colors truncate"
                        >
                          Website
                        </a>
                      </div>
                    )}
                    {lead.twitter && (
                      <div className="flex items-center gap-grid-2 min-w-0">
                        <span className="text-accent flex-shrink-0">🐦</span>
                        <a 
                          href={`https://twitter.com/${lead.twitter.replace('@', '')}`}
                          target="_blank" 
                          rel="noopener noreferrer"
                          className="text-size-4 text-accent hover:text-foreground transition-colors truncate"
                        >
                          @{lead.twitter.replace('@', '')}
                        </a>
                      </div>
                    )}
                    {lead.pocName && (
                      <div className="flex items-center gap-grid-2 min-w-0">
                        <span className="text-accent flex-shrink-0">👤</span>
                        <span className="text-size-4 text-foreground truncate">
                          {lead.pocName} {lead.pocRole && `(${lead.pocRole})`}
                        </span>
                      </div>
                    )}
                    {lead.referralLinkId && (
                      <div className="flex items-center gap-grid-2 min-w-0">
                        <span className="text-accent flex-shrink-0">🔗</span>
                        <span className="text-size-4 text-accent truncate">Referral Lead</span>
                      </div>
                    )}
                  </div>

                  {/* Notes */}
                  {lead.notes && (
                    <div className="p-grid-4 bg-surface-2 rounded-lg">
                      <p className="text-size-4 text-paragraph break-words">{lead.notes}</p>
                    </div>
                  )}

                  {/* Telegram group link */}
                  {lead.telegramGroupUrl && (
                    <div className="flex items-center gap-grid-2 p-grid-3 bg-accent/10 rounded-lg">
                      <span className="text-accent flex-shrink-0">💬</span>
                      <a
                        href={lead.telegramGroupUrl}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="text-size-4 text-accent hover:text-foreground transition-colors font-semibold"
                      >
                        Join Telegram Group →
                      </a>
                    </div>
                  )}

                  {/* Actions */}
                  {(canUpdateStatus || (canApproveLeads && lead.approved === undefined)) && (
                    <div className="flex flex-col sm:flex-row gap-grid-3 pt-grid-4 border-t border-border-subtle">
                      {canUpdateStatus && (
                        <div className="flex-1">
                          <select
                            value={lead.status}
                            onChange={(e) => handleStatusUpdate(lead._id, e.target.value as any)}
                            className="auth-input-field text-size-4 py-grid-2 w-full"
                          >
                            <option value="warm">🔥 Warm</option>
                            <option value="cold">❄️ Cold</option>
                            <option value="won">🏆 Won</option>
                            <option value="lost">💔 Lost</option>
                          </select>
                        </div>
                      )}

                      {canApproveLeads && lead.approved === undefined && (
                        <div className="flex gap-grid-2 flex-1 sm:flex-none">
                          <button
                            onClick={() => handleApprove(lead._id, false)}
                            className="btn-secondary text-destructive border-destructive hover:bg-destructive hover:text-destructive-foreground flex-1 sm:w-auto"
                          >
                            ❌ Reject
                          </button>
                          <button
                            onClick={() => {
                              const telegramUrl = prompt("Telegram group URL (optional):");
                              handleApprove(lead._id, true, telegramUrl || undefined);
                            }}
                            className="btn-secondary text-status-success border-status-success hover:bg-status-success hover:text-card-foreground flex-1 sm:w-auto"
                          >
                            ✅ Approve
                          </button>
                        </div>
                      )}
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
