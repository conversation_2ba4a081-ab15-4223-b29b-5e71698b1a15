import { useState, useEffect } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

export function ProfileSetup() {
  const user = useQuery(api.auth.loggedInUser);
  const completeProfile = useMutation(api.users.completeProfile);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    email: "",
    fullName: "",
    preferredCommunication: [] as string[],
    telegram: "",
    whatsapp: "",
    emailDetails: "",
    companyName: "",
    xProfile: "",
    companyType: "",
    companyTypeOther: "",
    roleTitle: "",
    roleTitleOther: "",
    preferredPaymentMethod: "",
    termsAccepted: false,
  });

  const companyTypes = [
    "Venture Capital (VC)",
    "Central Exchange (CEX)",
    "Decentralized Exchange (DEX)",
    "Launchpad",
    "Marketing Agency",
    "Incubator/Accelerator",
    "Market Making (MM)",
    "Development",
    "Technology",
    "Research",
    "Data and aggregation platform",
    "External BD",
    "Deal Flow Individual",
    "Angel Investor",
    "Others"
  ];

  const roleTitles = ["CEO", "CTO", "CMO", "BD", "Founder", "Co-Founder", "Others"];
  const communicationMethods = ["whatsapp", "email", "telegram"];

  // Auto-fill email when user data is available
  useEffect(() => {
    if (user?.email && !formData.email) {
      setFormData(prev => ({ ...prev, email: user.email || "" }));
    }
  }, [user?.email, formData.email]);

  const handleCommunicationChange = (method: string, checked: boolean) => {
    console.log(`Communication method ${method} ${checked ? 'selected' : 'deselected'}`);
    if (checked) {
      setFormData({ ...formData, preferredCommunication: [...formData.preferredCommunication, method] });
    } else {
      setFormData({ ...formData, preferredCommunication: formData.preferredCommunication.filter(m => m !== method) });
    }
  };

  const sanitizeInput = (input: string, maxLength: number) => {
    return input.replace(/[<>"'&]/g, '').substring(0, maxLength).trim();
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Validation
    if (!formData.email || !formData.fullName || !formData.companyName || 
        !formData.companyType || !formData.roleTitle || 
        !formData.preferredPaymentMethod || !formData.termsAccepted) {
      toast.error("Please fill in all required fields and accept the terms");
      return;
    }

    if (formData.preferredCommunication.length === 0) {
      toast.error("Please select at least one communication method");
      return;
    }

    // Validate communication method details
    if (formData.preferredCommunication.includes('telegram') && !formData.telegram) {
      toast.error("Please provide your Telegram handle");
      return;
    }

    if (formData.preferredCommunication.includes('whatsapp') && !formData.whatsapp) {
      toast.error("Please provide your WhatsApp number");
      return;
    }

    if (formData.companyType === "Others" && !formData.companyTypeOther) {
      toast.error("Please specify the company type");
      return;
    }

    if (formData.roleTitle === "Others" && !formData.roleTitleOther) {
      toast.error("Please specify your role title");
      return;
    }

    try {
      setIsSubmitting(true);
      console.log('Submitting profile with data:', formData);

      // Sanitize inputs
      const sanitizedData = {
        email: sanitizeInput(formData.email, 100),
        fullName: sanitizeInput(formData.fullName, 100),
        preferredCommunication: formData.preferredCommunication as ("whatsapp" | "email" | "telegram")[],
        telegram: formData.telegram ? sanitizeInput(formData.telegram, 50) : undefined,
        whatsapp: formData.whatsapp ? sanitizeInput(formData.whatsapp, 20) : undefined,
        emailDetails: formData.emailDetails ? sanitizeInput(formData.emailDetails, 500) : undefined,
        companyName: sanitizeInput(formData.companyName, 100),
        xProfile: formData.xProfile ? sanitizeInput(formData.xProfile, 100) : undefined,
        companyType: formData.companyType === "Others" ? sanitizeInput(formData.companyTypeOther, 100) : formData.companyType,
        roleTitle: formData.roleTitle === "Others" ? sanitizeInput(formData.roleTitleOther, 50) : formData.roleTitle,
        preferredPaymentMethod: formData.preferredPaymentMethod as "bank" | "usdt",
      };

      console.log('Sending sanitized data to backend:', sanitizedData);
      const result = await completeProfile(sanitizedData);
      console.log('Profile completion result:', result);
      toast.success(`Profile completed successfully! Your referral code: ${result.referralCode}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to complete profile");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen py-grid-8" style={{ backgroundColor: 'var(--background)' }}>
      <div className="max-w-4xl mx-auto px-grid-4">
        {/* Header Card */}
        <div className="rounded-lg p-grid-6 mb-grid-6 text-center" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
          <h1 className="text-size-1 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>
            Complete Your Partner Profile
          </h1>
          <p className="text-size-3" style={{ color: 'var(--muted)' }}>
            This information is required to access the partner portal
          </p>
        </div>

        {/* Required Notice */}
        <div className="rounded-lg p-grid-4 mb-grid-6" style={{ backgroundColor: 'var(--surface-2)', border: '1px solid var(--border-subtle)' }}>
          <p className="text-size-4" style={{ color: 'var(--muted)' }}>
            <strong style={{ color: 'var(--foreground)' }}>Required:</strong> You must complete this profile to access partner features.
            Your referral link will be automatically generated based on your information.
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-grid-6">
          {/* Personal Information */}
          <div className="rounded-lg p-grid-6" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
            <h3 className="text-size-2 font-semibold mb-grid-4" style={{ color: 'var(--foreground)' }}>Personal Information</h3>

            <div className="space-y-grid-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Email Address *</label>
                  <input
                    type="email"
                    value={formData.email}
                    onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    required
                    maxLength={100}
                  />
                </div>

                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Full Name *</label>
                  <input
                    type="text"
                    value={formData.fullName}
                    onChange={(e) => setFormData({ ...formData, fullName: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    required
                    maxLength={100}
                  />
                </div>
              </div>

              <div>
                <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>
                  Preferred Communication Methods * (Select at least one)
                </label>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-grid-4">
                  {communicationMethods.map((method) => (
                    <label key={method} className="flex items-center gap-grid-2 cursor-pointer p-grid-3 rounded-lg border transition-all duration-200 hover:bg-opacity-50"
                           style={{
                             backgroundColor: formData.preferredCommunication.includes(method) ? 'var(--accent-bg)' : 'var(--surface-2)',
                             borderColor: formData.preferredCommunication.includes(method) ? 'var(--accent)' : 'var(--border-default)'
                           }}>
                      <input
                        type="checkbox"
                        checked={formData.preferredCommunication.includes(method)}
                        onChange={(e) => handleCommunicationChange(method, e.target.checked)}
                        className="w-4 h-4 rounded border"
                        style={{
                          backgroundColor: formData.preferredCommunication.includes(method) ? 'var(--accent)' : 'var(--surface-2)',
                          borderColor: 'var(--border-default)'
                        }}
                      />
                      <span className="capitalize text-size-4 font-medium" style={{ color: 'var(--foreground)' }}>{method}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Conditional Communication Details */}
              {formData.preferredCommunication.includes('telegram') && (
                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>
                    Telegram Handle *
                  </label>
                  <input
                    type="text"
                    value={formData.telegram}
                    onChange={(e) => setFormData({ ...formData, telegram: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    placeholder="@username"
                    maxLength={50}
                    required
                  />
                </div>
              )}

              {formData.preferredCommunication.includes('whatsapp') && (
                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>
                    WhatsApp Number *
                  </label>
                  <input
                    type="text"
                    value={formData.whatsapp}
                    onChange={(e) => setFormData({ ...formData, whatsapp: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    placeholder="+1234567890"
                    maxLength={20}
                    required
                  />
                </div>
              )}

              {formData.preferredCommunication.includes('email') && (
                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>
                    Email Communication Preferences
                  </label>
                  <textarea
                    value={formData.emailDetails}
                    onChange={(e) => setFormData({ ...formData, emailDetails: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    placeholder="Specify your email preferences, best times to contact, or any special instructions..."
                    rows={3}
                    maxLength={500}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Company Information */}
          <div className="rounded-lg p-grid-6" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
            <h3 className="text-size-2 font-semibold mb-grid-4" style={{ color: 'var(--foreground)' }}>Company Information</h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
              <div>
                <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Company Name *</label>
                <input
                  type="text"
                  value={formData.companyName}
                  onChange={(e) => setFormData({ ...formData, companyName: e.target.value })}
                  className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                  style={{
                    backgroundColor: 'var(--surface-2)',
                    color: 'var(--foreground)',
                    borderColor: 'var(--border-default)'
                  }}
                  required
                  maxLength={100}
                />
              </div>

              <div>
                <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>X (Twitter) Profile</label>
                <input
                  type="url"
                  value={formData.xProfile}
                  onChange={(e) => setFormData({ ...formData, xProfile: e.target.value })}
                  className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                  style={{
                    backgroundColor: 'var(--surface-2)',
                    color: 'var(--foreground)',
                    borderColor: 'var(--border-default)'
                  }}
                  placeholder="https://x.com/username"
                  maxLength={100}
                />
              </div>
            </div>


            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
              <div>
                <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Company Type *</label>
                <select
                  value={formData.companyType}
                  onChange={(e) => setFormData({ ...formData, companyType: e.target.value })}
                  className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 pr-10 partner-form-input appearance-none bg-no-repeat bg-right"
                  style={{
                    backgroundColor: 'var(--surface-2)',
                    color: 'var(--foreground)',
                    borderColor: 'var(--border-default)',
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: 'right 12px center',
                    backgroundSize: '16px'
                  }}
                  required
                >
                  <option value="">Select company type</option>
                  {companyTypes.map((type) => (
                    <option key={type} value={type}>{type}</option>
                  ))}
                </select>
              </div>

              <div>
                <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Your Role *</label>
                <select
                  value={formData.roleTitle}
                  onChange={(e) => setFormData({ ...formData, roleTitle: e.target.value })}
                  className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 pr-10 partner-form-input appearance-none bg-no-repeat bg-right"
                  style={{
                    backgroundColor: 'var(--surface-2)',
                    color: 'var(--foreground)',
                    borderColor: 'var(--border-default)',
                    backgroundImage: `url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e")`,
                    backgroundPosition: 'right 12px center',
                    backgroundSize: '16px'
                  }}
                  required
                >
                  <option value="">Select your role</option>
                  {roleTitles.map((role) => (
                    <option key={role} value={role}>{role}</option>
                  ))}
                </select>
              </div>
            </div>

            {/* Conditional fields for "Others" selections */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
              {formData.companyType === "Others" && (
                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Specify Company Type *</label>
                  <input
                    type="text"
                    value={formData.companyTypeOther}
                    onChange={(e) => setFormData({ ...formData, companyTypeOther: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    required
                    maxLength={100}
                  />
                </div>
              )}

              {formData.roleTitle === "Others" && (
                <div>
                  <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Specify Your Role *</label>
                  <input
                    type="text"
                    value={formData.roleTitleOther}
                    onChange={(e) => setFormData({ ...formData, roleTitleOther: e.target.value })}
                    className="w-full rounded-lg border transition-all duration-200 outline-none text-size-3 p-grid-3 partner-form-input"
                    style={{
                      backgroundColor: 'var(--surface-2)',
                      color: 'var(--foreground)',
                      borderColor: 'var(--border-default)'
                    }}
                    required
                    maxLength={50}
                  />
                </div>
              )}
            </div>
          </div>

          {/* Payment Details */}
          <div className="rounded-lg p-grid-6" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
            <h3 className="text-size-2 font-semibold mb-grid-4" style={{ color: 'var(--foreground)' }}>Payment Details</h3>

            <div>
              <label className="block text-size-4 font-semibold mb-grid-2" style={{ color: 'var(--foreground)' }}>Preferred Payment Method *</label>
              <div className="flex flex-wrap gap-grid-4">
                <label className="flex items-center gap-grid-2 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="bank"
                    checked={formData.preferredPaymentMethod === "bank"}
                    onChange={(e) => setFormData({ ...formData, preferredPaymentMethod: e.target.value })}
                    className="w-4 h-4 rounded-full border"
                    style={{
                      backgroundColor: formData.preferredPaymentMethod === "bank" ? 'var(--accent)' : 'var(--surface-2)',
                      borderColor: 'var(--border-default)'
                    }}
                  />
                  <span className="text-size-4" style={{ color: 'var(--foreground)' }}>Bank Transfer</span>
                </label>
                <label className="flex items-center gap-grid-2 cursor-pointer">
                  <input
                    type="radio"
                    name="paymentMethod"
                    value="usdt"
                    checked={formData.preferredPaymentMethod === "usdt"}
                    onChange={(e) => setFormData({ ...formData, preferredPaymentMethod: e.target.value })}
                    className="w-4 h-4 rounded-full border"
                    style={{
                      backgroundColor: formData.preferredPaymentMethod === "usdt" ? 'var(--accent)' : 'var(--surface-2)',
                      borderColor: 'var(--border-default)'
                    }}
                  />
                  <span className="text-size-4" style={{ color: 'var(--foreground)' }}>USDT</span>
                </label>
              </div>
            </div>
          </div>

          {/* Terms & Conditions */}
          <div className="rounded-lg p-grid-6" style={{ backgroundColor: 'var(--surface-1)', border: '1px solid var(--border-default)' }}>
            <h3 className="text-size-2 font-semibold mb-grid-4" style={{ color: 'var(--foreground)' }}>Terms & Conditions</h3>

            <label className="flex items-start gap-grid-3 cursor-pointer">
              <input
                type="checkbox"
                checked={formData.termsAccepted}
                onChange={(e) => setFormData({ ...formData, termsAccepted: e.target.checked })}
                className="w-4 h-4 rounded border mt-1"
                style={{
                  backgroundColor: formData.termsAccepted ? 'var(--accent)' : 'var(--surface-2)',
                  borderColor: 'var(--border-default)'
                }}
                required
              />
              <span className="text-size-4" style={{ color: 'var(--muted)' }}>
                I have read and agree to the{' '}
                <a href="#" className="hover:underline" style={{ color: 'var(--accent)' }}>Terms & Conditions</a>{' '}
                and{' '}
                <a href="#" className="hover:underline" style={{ color: 'var(--accent)' }}>Privacy Policy</a>.
                I understand that my application will be reviewed and I will be notified of the approval status. *
              </span>
            </label>
          </div>

          {/* Submit Button */}
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full rounded-lg font-semibold transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-size-3 p-grid-4 hover:transform hover:-translate-y-1"
            style={{
              backgroundColor: 'var(--accent)',
              color: 'var(--accent-foreground)',
              boxShadow: isSubmitting ? 'none' : '0 4px 12px oklch(from var(--accent) l c h / 0.3)'
            }}
            onMouseEnter={(e) => {
              if (!isSubmitting) {
                e.currentTarget.style.boxShadow = '0 8px 25px oklch(from var(--accent) l c h / 0.4)';
              }
            }}
            onMouseLeave={(e) => {
              if (!isSubmitting) {
                e.currentTarget.style.boxShadow = '0 4px 12px oklch(from var(--accent) l c h / 0.3)';
              }
            }}
          >
            {isSubmitting ? "Completing Profile..." : "Complete Partner Profile"}
          </button>
        </form>
      </div>
    </div>
  );
}
