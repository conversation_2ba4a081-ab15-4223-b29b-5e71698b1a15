import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState } from "react";
import { toast } from "sonner";

export function Earnings() {
  const user = useQuery(api.users.myProfile);
  const earnings = useQuery(api.earnings.overview, {});
  const withdrawals = useQuery(api.earnings.listWithdrawals, {});
  const requestWithdrawal = useMutation(api.earnings.requestWithdrawal);
  
  const [isRequesting, setIsRequesting] = useState(false);
  const [formData, setFormData] = useState({
    amountUsd: "",
    method: "usdt" as "usdt" | "bank",
    walletAddress: "",
    bankDetails: "",
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const amount = parseFloat(formData.amountUsd);
    
    if (!amount || amount < 100) {
      toast.error("Minimum withdrawal amount is $100");
      return;
    }

    if (formData.method === "usdt" && !formData.walletAddress) {
      toast.error("Wallet address is required for USDT withdrawals");
      return;
    }

    if (formData.method === "bank" && !formData.bankDetails) {
      toast.error("Bank details are required for bank withdrawals");
      return;
    }

    try {
      await requestWithdrawal({
        amountUsd: amount,
        method: formData.method,
        walletAddress: formData.walletAddress || undefined,
        bankDetails: formData.bankDetails || undefined,
      });
      toast.success("Withdrawal request submitted successfully");
      setFormData({
        amountUsd: "",
        method: "usdt",
        walletAddress: "",
        bankDetails: "",
      });
      setIsRequesting(false);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to request withdrawal");
    }
  };

  // Debug logging
  console.log('🔍 EARNINGS COMPONENT DEBUG:');
  console.log('👤 User:', user);
  console.log('💰 Earnings:', earnings);
  console.log('📤 Withdrawals:', withdrawals);

  if (!user || earnings === undefined || withdrawals === undefined) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-accent border-t-transparent"></div>
          <p className="text-size-4" style={{ color: "var(--muted-foreground)" }}>Loading earnings...</p>
        </div>
      </div>
    );
  }

  const canRequestWithdrawal = user.role === "partner";

  return (
    <div className="space-y-grid-8 animate-fade-in">
      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-grid-4">
        <div className="space-y-grid-2">
          <h1 className="text-size-1">Earnings</h1>
          <p className="text-size-3" style={{ color: "var(--muted-foreground)" }}>Track your earnings and manage withdrawal requests</p>
        </div>
        {canRequestWithdrawal && earnings.pending > 0 && (
          <button
            onClick={() => setIsRequesting(!isRequesting)}
            className={`btn-gradient hover-lift ${isRequesting ? 'bg-destructive hover:bg-destructive' : ''}`}
          >
            {isRequesting ? "✕ Cancel" : "💰 Request Withdrawal"}
          </button>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-grid-6">
        <div className="metric-card hover-lift animate-slide-up">
          <div className="card-content">
            <div className="flex items-center justify-between mb-grid-4">
              <div className="flex items-center gap-grid-3">
                <div className="w-10 h-10 bg-accent/20 rounded-lg flex items-center justify-center">
                  <span className="text-accent text-size-2">📊</span>
                </div>
                <h3 className="text-size-4 font-semibold" style={{ color: "var(--muted-foreground)" }}>Estimated Earnings</h3>
              </div>
            </div>
            <p className="text-size-1 font-semibold text-foreground">
              ${earnings.estimated.toLocaleString()}
            </p>
            <p className="text-size-4 mt-grid-2" style={{ color: "var(--muted-foreground)" }}>
              Total projected earnings
            </p>
          </div>
        </div>
        
        <div className="metric-card metric-card-success hover-lift animate-slide-up" style={{ animationDelay: "0.1s" }}>
          <div className="card-content">
            <div className="flex items-center justify-between mb-grid-4">
              <div className="flex items-center gap-grid-3">
                <div className="w-10 h-10 bg-status-success/20 rounded-lg flex items-center justify-center">
                  <span style={{ color: "var(--status-success)" }} className="text-size-2">✅</span>
                </div>
                <h3 className="text-size-4 font-semibold" style={{ color: "var(--muted-foreground)" }}>Withdrawn</h3>
              </div>
            </div>
            <p className="text-size-1 font-semibold text-foreground">
              ${earnings.withdrawn.toLocaleString()}
            </p>
            <p className="text-size-4 mt-grid-2" style={{ color: "var(--muted-foreground)" }}>
              Successfully paid out
            </p>
          </div>
        </div>
        
        <div className="metric-card hover-lift animate-slide-up" style={{ animationDelay: "0.2s" }}>
          <div className="card-content">
            <div className="flex items-center justify-between mb-grid-4">
              <div className="flex items-center gap-grid-3">
                <div className="w-10 h-10 bg-status-warning/20 rounded-lg flex items-center justify-center">
                  <span style={{ color: "var(--status-warning)" }} className="text-size-2">💰</span>
                </div>
                <h3 className="text-size-4 font-semibold" style={{ color: "var(--muted-foreground)" }}>Available</h3>
              </div>
              {earnings.pending >= 100 && (
                <span className="status-badge status-success text-size-4">
                  Ready to withdraw
                </span>
              )}
            </div>
            <p className="text-size-1 font-semibold text-foreground">
              ${earnings.pending.toLocaleString()}
            </p>
            <p className="text-size-4 mt-grid-2" style={{ color: "var(--muted-foreground)" }}>
              {earnings.pending >= 100 ? "Minimum met ($100)" : `Need $${(100 - earnings.pending).toFixed(0)} more`}
            </p>
          </div>
        </div>
      </div>

      {canRequestWithdrawal && isRequesting && (
        <div className="glass-card animate-scale-in">
          <div className="card-content">
            <div className="flex items-center gap-grid-3 mb-grid-6">
              <div className="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
                <span className="text-accent-foreground text-size-2">💸</span>
              </div>
              <h2 className="text-size-2">Request Withdrawal</h2>
            </div>
            <form onSubmit={handleSubmit} className="space-y-grid-6">
              <div className="form-floating">
                <input
                  type="number"
                  min="100"
                  step="0.01"
                  value={formData.amountUsd}
                  onChange={(e) => setFormData({ ...formData, amountUsd: e.target.value })}
                  placeholder=" "
                  required
                  className="hover-glow"
                />
                <label>Amount (USD) *</label>
              </div>
              <p className="text-size-4 mt-grid-1" style={{ color: "var(--muted-foreground)" }}>Minimum withdrawal: $100</p>

              <div>
                <select
                  value={formData.method}
                  onChange={(e) => setFormData({ ...formData, method: e.target.value as "usdt" | "bank" })}
                  className="auth-input-field text-size-3 py-grid-3"
                >
                  <option value="usdt">💰 USDT (Crypto)</option>
                  <option value="bank">🏦 Bank Transfer</option>
                </select>
              </div>

              {formData.method === "usdt" && (
                <div className="form-floating">
                  <input
                    type="text"
                    value={formData.walletAddress}
                    onChange={(e) => setFormData({ ...formData, walletAddress: e.target.value })}
                    placeholder=" "
                    required
                    className="hover-glow"
                  />
                  <label>USDT Wallet Address *</label>
                </div>
              )}

              {formData.method === "bank" && (
                <div className="form-floating">
                  <textarea
                    value={formData.bankDetails}
                    onChange={(e) => setFormData({ ...formData, bankDetails: e.target.value })}
                    rows={3}
                    placeholder=" "
                    required
                    className="auth-input-field resize-none"
                  />
                  <label>Bank Details *</label>
                </div>
              )}

              <div className="flex gap-grid-4 pt-grid-4">
                <button
                  type="submit"
                  className="btn-gradient hover-lift flex-1 sm:flex-none"
                >
                  💸 Submit Request
                </button>
                <button
                  type="button"
                  onClick={() => setIsRequesting(false)}
                  className="btn-secondary hover-lift"
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="space-y-grid-6">
        <div className="flex items-center gap-grid-3">
          <div className="w-8 h-8 bg-accent/20 rounded-lg flex items-center justify-center">
            <span className="text-accent text-size-4">📋</span>
          </div>
          <h2 className="text-size-2">Withdrawal History</h2>
        </div>
        
        <div className="space-y-grid-4">
          {withdrawals.length === 0 ? (
            <div className="glass-card">
              <div className="card-content text-center py-grid-8">
                <div className="w-16 h-16 bg-muted/20 rounded-full flex items-center justify-center mx-auto mb-grid-6">
                  <span className="text-size-1">📋</span>
                </div>
                <h3 className="text-size-2 mb-grid-2">No withdrawals yet</h3>
                <p className="text-size-3" style={{ color: "var(--muted-foreground)" }}>
                  Your withdrawal requests will appear here once submitted.
                </p>
              </div>
            </div>
          ) : (
            withdrawals.map((withdrawal, index) => (
              <div 
                key={withdrawal._id} 
                className="glass-card hover-lift animate-slide-up"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="card-content">
                  <div className="flex flex-col sm:flex-row sm:justify-between sm:items-start gap-grid-4">
                    <div className="flex-1 space-y-grid-3">
                      {/* Header with amount and method */}
                      <div className="flex items-center gap-grid-3">
                        <div className="w-10 h-10 bg-gradient-surface rounded-lg flex items-center justify-center">
                          <span className="text-foreground text-size-2">
                            {withdrawal.method === "usdt" ? "💰" : "🏦"}
                          </span>
                        </div>
                        <div>
                          <h3 className="text-size-2 font-semibold">${withdrawal.amountUsd.toLocaleString()}</h3>
                          <p className="text-size-4" style={{ color: "var(--muted-foreground)" }}>
                            {new Date(withdrawal._creationTime).toLocaleDateString()} • {withdrawal.method.toUpperCase()}
                          </p>
                        </div>
                      </div>

                      {/* Transaction details */}
                      {withdrawal.txIdOrRef && (
                        <div className="p-grid-3 bg-surface-2 rounded-lg">
                          <p className="text-size-4">
                            <span style={{ color: "var(--muted-foreground)" }}>Transaction: </span>
                            <span className="font-mono text-foreground">{withdrawal.txIdOrRef}</span>
                          </p>
                        </div>
                      )}
                      
                      {withdrawal.method === "usdt" && withdrawal.walletAddress && (
                        <div className="p-grid-3 bg-surface-2 rounded-lg">
                          <p className="text-size-4">
                            <span style={{ color: "var(--muted-foreground)" }}>Wallet: </span>
                            <span className="font-mono text-foreground break-all">{withdrawal.walletAddress}</span>
                          </p>
                        </div>
                      )}
                    </div>

                    {/* Status badge */}
                    <div className="flex justify-end">
                      <span className={`status-badge ${
                        withdrawal.status === "paid" ? "status-success" :
                        withdrawal.status === "approved" ? "status-info" :
                        withdrawal.status === "in_review" ? "status-warning" :
                        "status-error"
                      }`}>
                        {withdrawal.status === "paid" ? "✅ Paid" :
                         withdrawal.status === "approved" ? "👍 Approved" :
                         withdrawal.status === "in_review" ? "⏳ In Review" :
                         "❌ Rejected"}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}
