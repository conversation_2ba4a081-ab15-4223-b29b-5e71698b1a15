import { useState } from "react";
import { InvoiceEditor } from "./InvoiceEditor";
import { Id } from "../../convex/_generated/dataModel";

interface InvoiceEditorPageProps {
  deal: {
    _id: Id<"deals">;
    projectName: string;
    status: "in_progress" | "closed" | "lost" | "paid";
    commissionDueFiatUsd?: number;
    commissionDueTokenUsd?: number;
    invoiceNumber?: string;
    invoiceStatus?: "draft" | "sent" | "paid" | "overdue" | "cancelled";
    invoiceGeneratedAt?: number;
    invoiceDueDate?: number;
    invoiceNotes?: string;
    dealValueUsd?: number;
    commissionPct?: number;
  };
  onBack: () => void;
}

export function InvoiceEditorPage({ deal, onBack }: InvoiceEditorPageProps) {
  const [isEditMode] = useState(true);

  const handleSave = () => {
    // Navigate back to invoices after save
    onBack();
  };

  const handleCancel = () => {
    // Navigate back to invoices
    onBack();
  };

  return (
    <div className="space-y-grid-6">
      {/* Page Header with Back Navigation */}
      <div className="flex items-center gap-grid-4">
        <button
          onClick={onBack}
          className="flex items-center gap-grid-2 btn-secondary hover-lift"
        >
          ← Back to Invoices
        </button>
        <div className="flex-1">
          <h1 className="text-size-1">Invoice Editor</h1>
          <p className="text-size-3 text-muted-foreground">
            {deal.invoiceNumber ? `Edit Invoice #${deal.invoiceNumber}` : 'Create New Invoice'} • {deal.projectName}
          </p>
        </div>
      </div>

      {/* Full Page Invoice Editor */}
      <div className="min-h-[calc(100vh-200px)]">
        <InvoiceEditor
          deal={deal}
          onSave={handleSave}
          onCancel={handleCancel}
        />
      </div>
    </div>
  );
}