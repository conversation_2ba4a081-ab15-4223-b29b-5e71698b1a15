import { useQuery, useMutation } from "convex/react";
import { api } from "../../convex/_generated/api";
import { Id } from "../../convex/_generated/dataModel";
import { useState, useRef } from "react";
import { toast } from "sonner";
import { InvoiceEditor } from "./InvoiceEditor";
import { generateInvoicePDF } from "../utils/generateInvoicePDF";

interface InvoiceGeneratorProps {
  deal: {
    _id: Id<"deals">;
    projectName: string;
    status: "in_progress" | "closed" | "lost" | "paid";
    commissionDueFiatUsd?: number;
    commissionDueTokenUsd?: number;
    invoiceNumber?: string;
    invoiceStatus?: "draft" | "sent" | "paid" | "overdue" | "cancelled";
    invoiceGeneratedAt?: number;
    invoiceDueDate?: number;
    invoiceNotes?: string;
  };
  showInvoiceEditor?: (deal: any) => void;
  onInvoiceGenerated?: () => void;
}

export function InvoiceGenerator({ deal, showInvoiceEditor: navigateToEditor, onInvoiceGenerated }: InvoiceGeneratorProps) {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showInvoiceEditor, setShowInvoiceEditor] = useState(false);
  const [showPreview, setShowPreview] = useState(false);
  const [invoiceNotes, setInvoiceNotes] = useState(deal.invoiceNotes || "");
  const [dueDate, setDueDate] = useState(() => {
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() + 30);
    return defaultDate.toISOString().split('T')[0];
  });
  
  const generateInvoice = useMutation(api.deals.generateInvoice);
  const updateInvoiceStatus = useMutation(api.deals.updateInvoiceStatus);
  const invoiceData = useQuery(api.deals.getInvoiceData, 
    showPreview ? { dealId: deal._id } : "skip"
  );
  
  const totalCommission = (deal.commissionDueFiatUsd || 0) + (deal.commissionDueTokenUsd || 0);
  const canGenerateInvoice = ["closed", "paid"].includes(deal.status) && totalCommission > 0;
  const hasExistingInvoice = !!deal.invoiceNumber;
  
  const handleStatusUpdate = async (newStatus: "draft" | "sent" | "paid" | "overdue" | "cancelled") => {
    try {
      await updateInvoiceStatus({
        dealId: deal._id,
        status: newStatus,
      });
      toast.success("Invoice status updated");
      onInvoiceGenerated?.();
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update status");
    }
  };
  
  const formatDate = (timestamp?: number) => {
    if (!timestamp) return "N/A";
    return new Date(timestamp).toLocaleDateString();
  };
  
  const getStatusColor = (status?: string) => {
    switch (status) {
      case "draft": return "bg-gray-500/20 text-gray-400";
      case "sent": return "bg-blue-500/20 text-blue-400";
      case "paid": return "bg-green-500/20 text-green-400";
      case "overdue": return "bg-red-500/20 text-red-400";
      case "cancelled": return "bg-gray-500/20 text-gray-400";
      default: return "bg-gray-500/20 text-gray-400";
    }
  };
  
  if (!canGenerateInvoice && !hasExistingInvoice) {
    return (
      <div className="text-size-4 text-muted-foreground">
        💡 Invoice generation available for closed/paid deals with commission
      </div>
    );
  }
  
  return (
    <div className="space-y-grid-4">
      {hasExistingInvoice ? (
        <div className="glass-card">
          <div className="card-content">
            <div className="flex items-center justify-between mb-grid-4">
              <div className="flex items-center gap-grid-3">
                <div className="w-8 h-8 bg-gradient-accent rounded-lg flex items-center justify-center">
                  <span className="text-accent-foreground text-size-4">📄</span>
                </div>
                <div>
                  <h3 className="text-size-3 font-semibold">Invoice {deal.invoiceNumber}</h3>
                  <p className="text-size-4 text-muted-foreground">
                    Generated: {formatDate(deal.invoiceGeneratedAt)}
                  </p>
                </div>
              </div>
              <span className={`px-2 py-1 rounded text-size-4 ${getStatusColor(deal.invoiceStatus)}`}>
                {deal.invoiceStatus?.charAt(0).toUpperCase() + deal.invoiceStatus?.slice(1) || "Draft"}
              </span>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4 mb-grid-4">
              <div>
                <p className="text-size-4 text-muted-foreground">Amount</p>
                <p className="text-size-3 font-semibold">${totalCommission.toLocaleString()}</p>
              </div>
              <div>
                <p className="text-size-4 text-muted-foreground">Due Date</p>
                <p className="text-size-3">{formatDate(deal.invoiceDueDate)}</p>
              </div>
            </div>
            
            {deal.invoiceNotes && (
              <div className="mb-grid-4">
                <p className="text-size-4 text-muted-foreground">Notes</p>
                <p className="text-size-4">{deal.invoiceNotes}</p>
              </div>
            )}
            
            <div className="flex flex-wrap gap-grid-2">
              <button
                onClick={() => setShowPreview(true)}
                className="btn-secondary hover-lift"
              >
                📄 Preview Invoice
              </button>

              <button
                onClick={() => navigateToEditor?.(deal)}
                className="btn-secondary hover-lift"
              >
                ✏️ Edit Invoice
              </button>
              
              {deal.invoiceStatus === "draft" && (
                <button
                  onClick={() => handleStatusUpdate("sent")}
                  className="btn-gradient hover-lift"
                >
                  📤 Mark as Sent
                </button>
              )}
              
              {deal.invoiceStatus === "sent" && (
                <button
                  onClick={() => handleStatusUpdate("paid")}
                  className="btn-gradient hover-lift"
                >
                  ✅ Mark as Paid
                </button>
              )}
              
              <select
                value={deal.invoiceStatus || "draft"}
                onChange={(e) => handleStatusUpdate(e.target.value as any)}
                className="auth-input-field text-size-4 py-grid-2 px-grid-3"
              >
                <option value="draft">Draft</option>
                <option value="sent">Sent</option>
                <option value="paid">Paid</option>
                <option value="overdue">Overdue</option>
                <option value="cancelled">Cancelled</option>
              </select>
            </div>
          </div>
        </div>
      ) : (
        <div className="flex flex-col gap-grid-3">
          <div className="glass-card">
            <div className="card-content">
              <div className="flex items-center gap-grid-3 mb-grid-4">
                <div className="w-8 h-8 bg-blue-500/20 rounded-lg flex items-center justify-center">
                  <span className="text-blue-400 text-size-4">📋</span>
                </div>
                <div>
                  <h3 className="text-size-3 font-semibold">Generate Invoice</h3>
                  <p className="text-size-4 text-muted-foreground">
                    Commission: ${totalCommission.toLocaleString()}
                  </p>
                </div>
              </div>
              
              <button
                onClick={() => navigateToEditor?.(deal)}
                className="btn-gradient hover-lift"
                disabled={isGenerating}
              >
                {isGenerating ? "⏳ Generating..." : "📝 Create Invoice"}
              </button>
            </div>
          </div>
        </div>
      )}
      
      {/* Invoice Preview Modal */}
      {showPreview && invoiceData && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-grid-4">
          <div className="glass-card max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <div className="card-content">
              <div className="flex items-center justify-between mb-grid-6">
                <div className="flex items-center gap-grid-3">
                  <div className="w-10 h-10 bg-gradient-accent rounded-lg flex items-center justify-center">
                    <span className="text-accent-foreground text-size-2">👁️</span>
                  </div>
                  <h2 className="text-size-2">Invoice Preview</h2>
                </div>
                <button
                  onClick={() => setShowPreview(false)}
                  className="text-muted-foreground hover:text-foreground"
                >
                  ✕
                </button>
              </div>
              
              {/* Professional Invoice Preview Content */}
              <div className="bg-white text-black shadow-lg">
                {/* Invoice Header */}
                <div className="bg-gradient-to-r from-blue-600 to-blue-800 text-white p-grid-6">
                  <div className="flex justify-between items-start">
                    <div>
                      <h1 className="text-2xl font-bold">INVOICE</h1>
                      <p className="text-blue-100 mt-1">#{invoiceData.invoiceNumber}</p>
                    </div>
                    <div className="text-right">
                      <div className="text-sm opacity-90">Invoice Date</div>
                      <div className="font-semibold">{formatDate(invoiceData.invoiceDate)}</div>
                      <div className="text-sm opacity-90 mt-2">Due Date</div>
                      <div className="font-semibold">{formatDate(invoiceData.dueDate)}</div>
                    </div>
                  </div>
                </div>

                <div className="p-grid-8">
                  {/* Company Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-8 mb-grid-8">
                    <div>
                      <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2">From</h3>
                      <div className="text-black">
                        <h2 className="text-xl font-bold mb-1">{invoiceData.fromCompany.name}</h2>
                        <div className="text-gray-700 space-y-1">
                          {invoiceData.fromCompany.address && <p>{invoiceData.fromCompany.address}</p>}
                          {invoiceData.fromCompany.email && <p>{invoiceData.fromCompany.email}</p>}
                          {invoiceData.fromCompany.phone && <p>{invoiceData.fromCompany.phone}</p>}
                          {invoiceData.fromCompany.vatNumber && (
                            <p>VAT: {invoiceData.fromCompany.vatNumber}</p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h3 className="text-sm font-semibold text-gray-500 uppercase tracking-wide mb-2">Bill To</h3>
                      <div className="text-black">
                        <h2 className="text-xl font-bold mb-1">{invoiceData.toCompany.name}</h2>
                        <div className="text-gray-700 space-y-1">
                          {invoiceData.toCompany.address && <p>{invoiceData.toCompany.address}</p>}
                          {invoiceData.toCompany.email && <p>{invoiceData.toCompany.email}</p>}
                          {invoiceData.toCompany.phone && <p>{invoiceData.toCompany.phone}</p>}
                          {invoiceData.toCompany.vatNumber && (
                            <p>VAT: {invoiceData.toCompany.vatNumber}</p>
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Services Table */}
                  <div className="mb-grid-8">
                    <div className="overflow-hidden border border-gray-200 rounded-lg">
                      <table className="w-full">
                        <thead>
                          <tr className="bg-gray-50">
                            <th className="px-grid-4 py-grid-3 text-left text-sm font-medium text-gray-900 border-b border-gray-200">Description</th>
                            <th className="px-grid-4 py-grid-3 text-center text-sm font-medium text-gray-900 border-b border-gray-200">Qty</th>
                            <th className="px-grid-4 py-grid-3 text-right text-sm font-medium text-gray-900 border-b border-gray-200">Rate</th>
                            <th className="px-grid-4 py-grid-3 text-right text-sm font-medium text-gray-900 border-b border-gray-200">Amount</th>
                          </tr>
                        </thead>
                        <tbody>
                          {invoiceData.services.map((service, index) => (
                            <tr key={index} className="border-b border-gray-100">
                              <td className="px-grid-4 py-grid-4">
                                <div className="font-medium text-gray-900">{service.description}</div>
                                {service.details && (
                                  <div className="text-sm text-gray-500 mt-1">
                                    Deal Value: {service.details.dealValue} • Commission: {service.details.commissionRate}
                                  </div>
                                )}
                              </td>
                              <td className="px-grid-4 py-grid-4 text-center text-gray-900">{service.quantity}</td>
                              <td className="px-grid-4 py-grid-4 text-right text-gray-900">${service.unitPrice.toFixed(2)}</td>
                              <td className="px-grid-4 py-grid-4 text-right font-medium text-gray-900">${service.total.toFixed(2)}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>
                  </div>
                  
                  {/* Totals */}
                  <div className="flex justify-end mb-grid-8">
                    <div className="w-80">
                      <div className="space-y-2">
                        <div className="flex justify-between py-2">
                          <span className="text-gray-600">Subtotal:</span>
                          <span className="text-gray-900">${invoiceData.financials.subtotal.toFixed(2)}</span>
                        </div>
                        {invoiceData.financials.vatRate > 0 && (
                          <div className="flex justify-between py-2">
                            <span className="text-gray-600">VAT ({invoiceData.financials.vatRate}%):</span>
                            <span className="text-gray-900">${invoiceData.financials.vatAmount.toFixed(2)}</span>
                          </div>
                        )}
                        <div className="border-t border-gray-200 pt-2">
                          <div className="flex justify-between py-2">
                            <span className="text-lg font-semibold text-gray-900">Total:</span>
                            <span className="text-lg font-bold text-gray-900">${invoiceData.financials.total.toFixed(2)}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  {/* Payment Information */}
                  <div className="bg-gray-50 p-grid-6 rounded-lg mb-grid-6">
                    <h4 className="font-semibold text-gray-900 mb-grid-3">Payment Information</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4 text-sm">
                      <div>
                        <p className="font-medium text-gray-700">Payment Due Date:</p>
                        <p className="text-gray-900">{formatDate(invoiceData.dueDate)}</p>
                      </div>
                      <div>
                        <p className="font-medium text-gray-700">Payment Terms:</p>
                        <p className="text-gray-900">Net 30 Days</p>
                      </div>
                    </div>
                  </div>

                  {/* Bank Details */}
                  <div className="bg-blue-50 border border-blue-200 p-grid-6 rounded-lg mb-grid-6">
                    <h4 className="font-semibold text-gray-900 mb-grid-3">Bank Transfer Details</h4>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4 text-sm">
                      <div className="space-y-2">
                        <p><span className="font-medium text-gray-700">Bank:</span> UNION BANK B.M.</p>
                        <p><span className="font-medium text-gray-700">Branch:</span> 079 Herzliya Pituah</p>
                        <p><span className="font-medium text-gray-700">Account:</span> ********</p>
                      </div>
                      <div className="space-y-2">
                        <p><span className="font-medium text-gray-700">IBAN:</span> IL5701307900000********</p>
                        <p><span className="font-medium text-gray-700">SWIFT:</span> UNBKILIT</p>
                        <p><span className="font-medium text-gray-700">Beneficiary:</span> {invoiceData.fromCompany.name}</p>
                      </div>
                    </div>
                  </div>
                  
                  {/* Notes */}
                  {invoiceData.notes && (
                    <div className="border-t border-gray-200 pt-grid-6">
                      <h4 className="font-semibold text-gray-900 mb-2">Notes:</h4>
                      <div className="text-gray-700 text-sm whitespace-pre-wrap">
                        {invoiceData.notes}
                      </div>
                    </div>
                  )}
                  
                  {/* Footer */}
                  <div className="text-center text-xs text-gray-500 mt-grid-8 pt-grid-4 border-t border-gray-200">
                    <p>Thank you for your business!</p>
                    <p className="mt-1">This invoice was generated electronically and is valid without signature.</p>
                  </div>
                </div>
              </div>
              
              <div className="flex gap-grid-4 pt-grid-6">
                <button
                  onClick={() => setShowPreview(false)}
                  className="btn-secondary hover-lift flex-1"
                >
                  Close Preview
                </button>
                <button
                  onClick={async () => {
                    if (invoiceData) {
                      await generateInvoicePDF(invoiceData);
                    }
                  }}
                  className="btn-gradient hover-lift flex-1"
                >
                  📥 Download as PDF
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}