import { toast } from "sonner";

export function PartnerResources() {
  return (
    <section aria-labelledby="partner-resources" className="card">
      <div className="card-header">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-grid-3">
            <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
              <svg className="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C20.832 18.477 19.246 18 17.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <div>
              <h3 id="partner-resources" className="text-size-2 text-foreground">Partner Resources</h3>
              <p className="text-size-4 text-muted">Essential tools and guides for your success</p>
            </div>
          </div>
        </div>
      </div>
      <div className="card-content">
        {/* Primary resources */}
        <div className="grid sm:grid-cols-2 lg:grid-cols-3 gap-grid-4">
          <ResourceCard
            title="Partner Guide"
            description="Complete guide to maximizing your partnership success"
            tone="primary"
            onClick={() => toast.info("Partner guide will be available soon")}
            icon={({ className }) => (
              <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
            )}
          />
          <ResourceCard
            title="Sales Materials"
            description="Presentations, case studies, and pitch decks"
            tone="accent"
            onClick={() => toast.info("Sales materials will be available soon")}
            icon={({ className }) => (
              <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21h10a2 2 0 002-2V9.414a1 1 0 00-.293-.707l-5.414-5.414A1 1 0 0012.586 3H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
              </svg>
            )}
          />
          <ResourceCard
            title="Support Center"
            description="Get help and contact your account manager"
            tone="muted"
            onClick={() => toast.info("Support center will be available soon")}
            icon={({ className }) => (
              <svg className={className} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
            )}
          />
        </div>

        {/* Secondary lists */}
        <div className="mt-grid-6 pt-grid-6 border-t border-border grid sm:grid-cols-2 gap-grid-4">
          <div className="space-y-grid-2">
            <h4 className="text-size-3 font-semibold text-foreground">Training & Webinars</h4>
            <ul className="space-y-grid-1 text-size-4 text-muted">
              <li className="flex items-center gap-grid-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full"></span>
                Monthly partner training sessions
              </li>
              <li className="flex items-center gap-grid-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full"></span>
                Product update webinars
              </li>
              <li className="flex items-center gap-grid-2">
                <span className="w-1.5 h-1.5 bg-accent rounded-full"></span>
                Sales technique workshops
              </li>
            </ul>
          </div>

          <div className="space-y-grid-2">
            <h4 className="text-size-3 font-semibold text-foreground">Quick Links</h4>
            <ul className="space-y-grid-1 text-size-4">
              <li>
                <a href="#" className="text-primary transition-colors">
                  Commission Structure Guide
                </a>
              </li>
              <li>
                <a href="#" className="text-primary transition-colors">
                  Lead Qualification Checklist
                </a>
              </li>
              <li>
                <a href="#" className="text-primary transition-colors">
                  Partner Agreement Terms
                </a>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </section>
  );
}

function ResourceCard({
  title,
  description,
  icon,
  onClick,
  tone = "primary",
}: {
  title: string;
  description: string;
  icon: (props: { className: string }) => JSX.Element;
  onClick: () => void;
  tone?: "primary" | "accent" | "muted";
}) {
  const toneBg =
    tone === "accent" ? "bg-accent/10 group-hover:bg-accent/20" :
    tone === "muted" ? "bg-secondary/20 group-hover:bg-secondary/30" :
    "bg-primary/10 group-hover:bg-primary/20";

  const toneIcon =
    tone === "accent" ? "text-accent" :
    tone === "muted" ? "text-foreground" :
    "text-primary";

  return (
    <button
      type="button"
      onClick={onClick}
      className="group text-left p-grid-6 border border-border rounded-xl hover:border-primary hover:bg-primary/5 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-accent/30"
    >
      <div className="space-y-grid-3">
        <div className={`w-12 h-12 ${toneBg} rounded-xl flex items-center justify-center transition-colors`}>
          {icon({ className: `w-6 h-6 ${toneIcon}` })}
        </div>
        <div className="space-y-grid-1">
          <h4 className="text-size-3 font-semibold text-foreground group-hover:text-primary transition-colors">
            {title}
          </h4>
          <p className="text-size-4 text-muted">{description}</p>
        </div>
      </div>
    </button>
  );
}

