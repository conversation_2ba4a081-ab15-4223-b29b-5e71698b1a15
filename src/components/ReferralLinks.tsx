import { useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { useState, useEffect } from "react";
import { toast } from "sonner";
import QRCode from "qrcode";

export function ReferralLinks() {
  const user = useQuery(api.users.myProfile);
  const [qrCodeUrl, setQrCodeUrl] = useState<string>("");

  const referralUrl = user?.defaultReferralCode 
    ? `${window.location.origin}/submit-lead?ref=${user.defaultReferralCode}`
    : "";

  useEffect(() => {
    if (referralUrl) {
      QRCode.toDataURL(referralUrl, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      })
      .then(url => setQrCodeUrl(url))
      .catch(err => console.error('Error generating QR code:', err));
    }
  }, [referralUrl]);

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      console.log('Copied to clipboard:', text);
      toast.success("Copied to clipboard");
    } catch (err) {
      console.error('Failed to copy:', err);
      toast.error("Failed to copy to clipboard");
    }
  };

  const downloadQRCode = () => {
    if (qrCodeUrl) {
      console.log('Downloading QR code for referral:', user?.defaultReferralCode);
      const link = document.createElement('a');
      link.download = `referral-qr-${user?.defaultReferralCode}.png`;
      link.href = qrCodeUrl;
      link.click();
      toast.success("QR code downloaded");
    }
  };

  if (!user) {
    return (
      <div className="flex justify-center items-center py-grid-8">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          <p className="text-size-4 text-muted">Loading referral links...</p>
        </div>
      </div>
    );
  }

  if (!user.approved) {
    return (
      <div className="flex items-center justify-center py-grid-12">
        <div className="card max-w-md">
          <div className="card-content text-center space-y-grid-4">
            <div className="w-16 h-16 bg-accent/10 rounded-2xl flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="space-y-grid-2">
              <h2 className="text-size-2 text-foreground">Access Pending</h2>
              <p className="text-size-3 text-muted">
                Your account needs to be approved before you can access referral links.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!user.defaultReferralCode) {
    return (
      <div className="flex items-center justify-center py-grid-12">
        <div className="card max-w-md">
          <div className="card-content text-center space-y-grid-4">
            <div className="w-16 h-16 bg-destructive/10 rounded-2xl flex items-center justify-center mx-auto">
              <svg className="w-8 h-8 text-destructive" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
            <div className="space-y-grid-2">
              <h2 className="text-size-2 text-foreground">No Referral Code</h2>
              <p className="text-size-3 text-muted">
                Contact an administrator to get your referral code assigned.
              </p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-grid-8">
      {/* Header Section */}
      <div className="space-y-grid-2">
        <h2 className="text-size-1 text-foreground">Your Referral Links</h2>
        <p className="text-size-3 text-muted">
          Share these links or QR codes to refer new leads and earn commissions.
        </p>
      </div>



      {/* Main Content Grid */}
      <div className="grid lg:grid-cols-2 gap-grid-6">
        {/* Referral Link Section */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center gap-grid-3">
              <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <div>
                <h3 className="text-size-2 text-foreground">Referral Links</h3>
                <p className="text-size-4 text-muted">Copy and share these links</p>
              </div>
            </div>
          </div>

          <div className="card-content space-y-grid-6">
            {/* Referral Code */}
            <div className="space-y-grid-2">
              <label className="text-size-4 font-medium text-foreground">
                Your Referral Code
              </label>
              <div className="flex items-center gap-grid-2">
                <input
                  type="text"
                  value={user.defaultReferralCode}
                  readOnly
                  className="flex-1 px-grid-3 py-grid-3 border border-border rounded-lg bg-muted/10 font-mono text-size-3 text-foreground"
                />
                <button
                  onClick={() => copyToClipboard(user.defaultReferralCode || "")}
                  className="btn-secondary px-grid-3 py-grid-3 flex items-center gap-grid-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span className="hidden sm:inline">Copy</span>
                </button>
              </div>
            </div>

            {/* Full URL */}
            <div className="space-y-grid-2">
              <label className="text-size-4 font-medium text-foreground">
                Full Referral URL
              </label>
              <div className="flex items-center gap-grid-2">
                <input
                  type="text"
                  value={referralUrl}
                  readOnly
                  className="flex-1 px-grid-3 py-grid-3 border border-border rounded-lg bg-muted/10 text-size-4 text-foreground overflow-hidden"
                />
                <button
                  onClick={() => copyToClipboard(referralUrl)}
                  className="btn-primary px-grid-3 py-grid-3 flex items-center gap-grid-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                  <span className="hidden sm:inline">Copy</span>
                </button>
              </div>
            </div>

            {/* Quick Share Buttons */}
            <div className="space-y-grid-2">
              <label className="text-size-4 font-medium text-foreground">
                Quick Share
              </label>
              <div className="grid grid-cols-2 gap-grid-2">
                <button
                  onClick={() => {
                    const text = `Check out this opportunity: ${referralUrl}`;
                    const url = `mailto:?subject=Business Opportunity&body=${encodeURIComponent(text)}`;
                    window.open(url);
                  }}
                  className="btn-secondary flex items-center justify-center gap-grid-2 py-grid-3"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                  </svg>
                  <span className="text-size-4">Email</span>
                </button>
                <button
                  onClick={() => {
                    if (navigator.share) {
                      navigator.share({
                        title: 'Business Opportunity',
                        text: 'Check out this opportunity',
                        url: referralUrl
                      });
                    } else {
                      copyToClipboard(referralUrl);
                    }
                  }}
                  className="btn-secondary flex items-center justify-center gap-grid-2 py-grid-3"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                  <span className="text-size-4">Share</span>
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* QR Code Section */}
        <div className="card">
          <div className="card-header">
            <div className="flex items-center gap-grid-3">
              <div className="w-10 h-10 bg-accent/10 rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h4M4 12h4m12 0h.01M5 8h2a1 1 0 001-1V5a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1zm12 0h2a1 1 0 001-1V5a1 1 0 00-1-1h-2a1 1 0 00-1 1v2a1 1 0 001 1zM5 20h2a1 1 0 001-1v-2a1 1 0 00-1-1H5a1 1 0 00-1 1v2a1 1 0 001 1z" />
                </svg>
              </div>
              <div>
                <h3 className="text-size-2 text-foreground">QR Code</h3>
                <p className="text-size-4 text-muted">For easy mobile sharing</p>
              </div>
            </div>
          </div>

          <div className="card-content">
            <div className="text-center space-y-grid-6">
              {qrCodeUrl ? (
                <>
                  <div className="inline-block p-grid-4 bg-card border border-border rounded-xl shadow-sm">
                    <img
                      src={qrCodeUrl}
                      alt="Referral QR Code"
                      className="w-48 h-48 mx-auto rounded-lg"
                    />
                  </div>
                  <div className="space-y-grid-4">
                    <button
                      onClick={downloadQRCode}
                      className="btn-primary w-full flex items-center justify-center gap-grid-2"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      Download QR Code
                    </button>
                    <div className="text-center space-y-grid-1">
                      <p className="text-size-4 text-muted">PNG format, 256×256 pixels</p>
                      <p className="text-size-4 text-muted">Perfect for printing or digital sharing</p>
                    </div>
                  </div>
                </>
              ) : (
                <div className="flex flex-col items-center justify-center py-grid-12 space-y-grid-4">
                  <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
                  <p className="text-size-4 text-muted">Generating QR code...</p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>


    </div>
  );
}
