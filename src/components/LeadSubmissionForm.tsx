import { useState, useEffect, type ReactNode } from "react";
import { useMutation, useQuery } from "convex/react";
import { api } from "../../convex/_generated/api";
import { toast } from "sonner";

interface LeadSubmissionFormProps {
  referralCode?: string;
}

export function LeadSubmissionForm({ referralCode }: LeadSubmissionFormProps) {
  const referralLink = useQuery(
    api.referrals.getLinkByCode,
    referralCode ? { code: referralCode } : "skip"
  );
  const submitLead = useMutation(api.referrals.submitLeadFromReferral);
  const trackClick = useMutation(api.referrals.trackClick);
  
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState({
    company: "",
    website: "",
    twitter: "",
    pocName: "",
    pocRole: "",
    notes: "",
  });

  // Simple completion ratio for progress UI
  const completion = (() => {
    const bits = [
      !!formData.company,
      !!formData.website,
      !!formData.twitter,
      !!formData.pocName,
      !!formData.pocRole,
      formData.notes.length > 0,
    ];
    const done = bits.filter(Boolean).length;
    return Math.round((done / bits.length) * 100);
  })();

  // Track the referral click when component loads
  useEffect(() => {
    if (referralCode && referralLink) {
      trackClick({
        code: referralCode,
        ip: undefined, // Could be populated from server
        userAgent: navigator.userAgent,
      }).catch(console.error);
    }
  }, [referralCode, referralLink, trackClick]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!referralCode) {
      toast.error("Invalid referral link");
      return;
    }

    if (!formData.company || !formData.pocName || !formData.pocRole) {
      toast.error("Please fill in all required fields");
      return;
    }

    try {
      setIsSubmitting(true);
      
      await submitLead({
        referralCode,
        company: formData.company,
        website: formData.website || undefined,
        twitter: formData.twitter || undefined,
        pocName: formData.pocName,
        pocRole: formData.pocRole,
        notes: formData.notes || undefined,
      });
      
      toast.success("Lead submitted successfully! We'll be in touch soon.");
      
      // Reset form
      setFormData({
        company: "",
        website: "",
        twitter: "",
        pocName: "",
        pocRole: "",
        notes: "",
      });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to submit lead");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (referralCode && referralLink === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <div className="animate-spin rounded-full h-6 w-6 border-2 border-primary border-t-transparent"></div>
      </div>
    );
  }

  if (referralCode && !referralLink) {
    return (
      <div className="text-center">
        <h2 className="text-size-2 mb-2">Invalid Referral Link</h2>
        <p className="text-size-4 text-muted">The referral code you used is not valid or has expired.</p>
      </div>
    );
  }

  return (
    <div>
      {/* Header inside card */}
      <div className="mb-grid-6">
        <div className="flex items-start justify-between gap-grid-4">
          <div>
            <h2 className="text-size-2">Tell Us About Your Project</h2>
            <p className="text-size-4 text-muted">We’ll respond within 1–2 business days.</p>
          </div>
          {referralLink && (
            <div className="status-badge-compact bg-surface-2 rounded-lg border border-border">
              <span className="font-semibold text-foreground">Referred by</span>
              <span>•</span>
              <span>{referralLink.ownerName}{referralLink.ownerCompany ? `, ${referralLink.ownerCompany}` : ""}</span>
            </div>
          )}
        </div>
        {/* Progress */}
        <div className="mt-grid-4">
          <div className="progress-track" aria-hidden>
            <div className="progress-bar" style={{ width: `${completion}%` }} />
          </div>
          <div className="flex justify-between text-size-4 text-muted mt-1">
            <span>Completion</span>
            <span>{completion}%</span>
          </div>
        </div>
      </div>

      <form onSubmit={handleSubmit} className="space-y-grid-6">
        {/* Row 1 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-6">
          <div>
            <label htmlFor="company" className="block text-size-4 font-semibold mb-grid-2">Company / Project Name *</label>
            <div className="input-with-icon">
              <input
                id="company"
                type="text"
                value={formData.company}
                onChange={(e) => setFormData({ ...formData, company: e.target.value })}
                className="auth-input-field pl-10 partner-form-input"
                placeholder="Enter your company or project name"
                required
                maxLength={100}
                aria-required
              />
              <FieldIcon>
                <BuildingIcon />
              </FieldIcon>
            </div>
          </div>

          <div>
            <label htmlFor="website" className="block text-size-4 font-semibold mb-grid-2">Website</label>
            <div className="input-with-icon">
              <input
                id="website"
                type="url"
                value={formData.website}
                onChange={(e) => setFormData({ ...formData, website: e.target.value })}
                className="auth-input-field pl-10 partner-form-input"
                placeholder="https://yourwebsite.com"
              />
              <FieldIcon>
                <LinkIcon />
              </FieldIcon>
            </div>
          </div>

          <div>
            <label htmlFor="twitter" className="block text-size-4 font-semibold mb-grid-2">X / Twitter</label>
            <div className="input-with-icon">
              <input
                id="twitter"
                type="text"
                value={formData.twitter}
                onChange={(e) => setFormData({ ...formData, twitter: e.target.value })}
                className="auth-input-field pl-10 partner-form-input"
                placeholder="@yourhandle or https://x.com/yourhandle"
              />
              <FieldIcon>
                <TwitterIcon />
              </FieldIcon>
            </div>
          </div>

          <div>
            <label htmlFor="pocName" className="block text-size-4 font-semibold mb-grid-2">Name of POC *</label>
            <div className="input-with-icon">
              <input
                id="pocName"
                type="text"
                value={formData.pocName}
                onChange={(e) => setFormData({ ...formData, pocName: e.target.value })}
                className="auth-input-field pl-10 partner-form-input"
                placeholder="Point of contact name"
                required
                maxLength={100}
                aria-required
              />
              <FieldIcon>
                <UserIcon />
              </FieldIcon>
            </div>
          </div>

          <div className="md:col-span-2">
            <label htmlFor="pocRole" className="block text-size-4 font-semibold mb-grid-2">Position of POC *</label>
            <div className="input-with-icon">
              <input
                id="pocRole"
                type="text"
                value={formData.pocRole}
                onChange={(e) => setFormData({ ...formData, pocRole: e.target.value })}
                className="auth-input-field pl-10 partner-form-input"
                placeholder="e.g., CEO, CTO, Founder, Business Development"
                required
                maxLength={100}
                aria-required
              />
              <FieldIcon>
                <BadgeIcon />
              </FieldIcon>
            </div>
          </div>
        </div>

        {/* Notes */}
        <div>
          <label htmlFor="notes" className="block text-size-4 font-semibold mb-grid-2">Notes / Comments (Optional)</label>
          <div className="input-with-icon">
            <textarea
              id="notes"
              value={formData.notes}
              onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
              rows={5}
              className="auth-input-field pl-10 partner-form-input"
              placeholder="Any additional context or requirements..."
              maxLength={500}
            />
            <FieldIcon>
              <NoteIcon />
            </FieldIcon>
          </div>
          <div className="mt-1 flex justify-between text-size-4 text-muted">
            <span>Optional</span>
            <span>{formData.notes.length}/500</span>
          </div>
        </div>

        <div className="flex flex-col sm:flex-row items-center justify-between gap-grid-3">
          <p className="text-size-4 text-muted">By submitting, you agree to be contacted about this request only.</p>
          <button
            type="submit"
            disabled={isSubmitting}
            className="btn-gradient w-full sm:w-auto disabled:opacity-60"
          >
            {isSubmitting ? (
              <span className="inline-flex items-center gap-2">
                <span className="animate-spin rounded-full h-4 w-4 border-2 border-[color:var(--accent-foreground)] border-t-transparent" />
                Submitting...
              </span>
            ) : (
              "Submit Lead"
            )}
          </button>
        </div>
      </form>
    </div>
  );
}

function FieldIcon({ children }: { children: ReactNode }) {
  return (
    <div className="input-icon" aria-hidden>
      {children}
    </div>
  );
}

function BuildingIcon() {
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M3 21h18v-2H3v2zm2-4h14V3H5v14zm2-2V5h10v10H7z" />
    </svg>
  );
}

function LinkIcon() {
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M3.9 12a5 5 0 015-5h3v2h-3a3 3 0 100 6h3v2h-3a5 5 0 01-5-5zm6-1h4v2h-4v-2zm5.1-4h-3V5h3a5 5 0 010 10h-3v-2h3a3 3 0 100-6z" />
    </svg>
  );
}

function TwitterIcon() {
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M20 7.2c.5-.3 1-.8 1.3-1.4-.5.2-1 .4-1.6.5A2.7 2.7 0 0012.7 8c0 .2 0 .4.1.6A7.6 7.6 0 014 6.2a2.7 2.7 0 00.8 3.6c-.4 0-.7-.1-1-.3v.1c0 1.3.9 2.5 2.1 2.8-.2 0-.4.1-.6.1-.1 0-.3 0-.4-.1.3 1 1.3 1.8 2.5 1.8A5.4 5.4 0 014 16.6a7.6 7.6 0 0011.7-6.4v-.3c.4-.2.8-.6 1.1-1z" />
    </svg>
  );
}

function UserIcon() {
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M12 12a5 5 0 100-10 5 5 0 000 10zm0 2c-4 0-8 2-8 6v2h16v-2c0-4-4-6-8-6z" />
    </svg>
  );
}

function BadgeIcon() {
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M17 3H7a2 2 0 00-2 2v14l7-3 7 3V5a2 2 0 00-2-2z" />
    </svg>
  );
}

function NoteIcon() {
  return (
    <svg className="w-4 h-4" viewBox="0 0 24 24" fill="currentColor">
      <path d="M4 3h14l3 3v12a3 3 0 01-3 3H4a3 3 0 01-3-3V6a3 3 0 013-3zm0 3v12h14V8h-4a1 1 0 01-1-1V4H4z" />
    </svg>
  );
}
