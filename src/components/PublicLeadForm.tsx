import { useEffect, useState } from "react";
import { LeadSubmissionForm } from "./LeadSubmissionForm";

export function PublicLeadForm() {
  const [referralCode, setReferralCode] = useState<string | undefined>();

  useEffect(() => {
    // Get referral code from URL parameters
    const urlParams = new URLSearchParams(window.location.search);
    const ref = urlParams.get("ref");
    if (ref) setReferralCode(ref);
  }, []);

  return (
    <div className="min-h-screen bg-background">
      {/* Hero */}
      <section className="relative overflow-hidden hero-surface">
        <div className="absolute inset-0 bg-gradient-to-b from-transparent to-[color:var(--surface-1)] opacity-80 pointer-events-none" />
        <div className="relative">
          <div className="max-w-5xl mx-auto px-grid-4 sm:px-grid-6 py-grid-8">
            <div className="flex items-center gap-grid-3">
              <img src="/logo.svg" alt="IBC Logo" className="w-10 h-10" />
              <div>
                <h1 className="text-size-1">Connect With IBC</h1>
                <p className="text-size-3 text-muted-foreground">Submit your project and get routed to the right team</p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content */}
      <main className="max-w-5xl mx-auto px-grid-4 sm:px-grid-6 -mt-6 pb-grid-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-grid-6">
          {/* Form Card */}
          <div className="lg:col-span-2 card">
            <div className="card-content">
              <LeadSubmissionForm referralCode={referralCode} />
            </div>
          </div>

          {/* Helpful Sidebar */}
          <aside className="glass-card lg:sticky lg:top-6 h-fit">
            <div className="card-content space-y-grid-4">
              <div>
                <h3 className="text-size-2">What Happens Next?</h3>
                <ul className="mt-2 space-y-2 text-size-4 text-muted">
                  <li className="flex items-start gap-2">
                    <CheckIcon />
                    <span>Your submission is reviewed by our operations team</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckIcon />
                    <span>If approved, we create a dedicated Telegram group</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckIcon />
                    <span>We connect you with the right internal specialists</span>
                  </li>
                  <li className="flex items-start gap-2">
                    <CheckIcon />
                    <span>We guide you through the next steps</span>
                  </li>
                </ul>
              </div>
              <div className="rounded-lg p-grid-4 bg-surface-2 border border-border">
                <h4 className="text-size-3 font-semibold">Privacy</h4>
                <p className="text-size-4 text-muted mt-1">We only use your details to process this request. No marketing emails or sharing with third parties.</p>
              </div>
            </div>
          </aside>
        </div>
      </main>
    </div>
  );
}

function CheckIcon() {
  return (
    <svg aria-hidden className="w-4 h-4 mt-1 text-[color:var(--accent)] flex-shrink-0" viewBox="0 0 20 20" fill="currentColor">
      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-7.25 7.25a1 1 0 01-1.414 0l-3-3a1 1 0 011.414-1.414l2.293 2.293 6.543-6.543a1 1 0 011.414 0z" clipRule="evenodd" />
    </svg>
  );
}
