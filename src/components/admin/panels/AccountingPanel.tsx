import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../../../convex/_generated/dataModel";

import { AdminLayout } from "../shared/AdminLayout";
import { NavigationTabs } from "../shared/NavigationTabs";
import { StatsCard } from "../shared/StatsCard";
import { DealTable } from "../shared/DealTable";
import { WithdrawalTable } from "../shared/WithdrawalTable";

export function AccountingPanel() {
  const [activeSection, setActiveSection] = useState("deals");
  
  // Data queries
  const allDeals = useQuery(api.deals.listForPartner, {});
  const allWithdrawals = useQuery(api.earnings.listWithdrawals, {});
  const financialSummary = useQuery(api.admin.getFinancialSummary);
  const allUsers = useQuery(api.users.list); // Get partner info for commission calculation
  
  // Mutations
  const updateDeal = useMutation(api.deals.update);
  const updateWithdrawalStatus = useMutation(api.earnings.updateWithdrawalStatus);
  const fixExistingDeals = useMutation(api.deals.fixExistingClosedDeals);
  
  // Handlers
  const handleUpdateDeal = async (dealId: Id<"deals">, updates: any) => {
    try {
      await updateDeal({ dealId, ...updates });
      toast.success("Deal updated successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  const handleUpdateWithdrawal = async (
    withdrawalId: Id<"withdrawals">, 
    status: "approved" | "paid" | "rejected", 
    txRef?: string
  ) => {
    try {
      await updateWithdrawalStatus({ withdrawalId, status, txIdOrRef: txRef });
      toast.success(`Withdrawal ${status}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update withdrawal");
    }
  };

  const handleFixExistingDeals = async () => {
    try {
      const result = await fixExistingDeals();
      toast.success(`Fixed ${result.dealsFixed} deals with missing commission values`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to fix existing deals");
    }
  };

  // Calculate stats
  const totalPendingWithdrawals = allWithdrawals?.filter(w => w.status === "in_review")
    .reduce((sum, w) => sum + w.amountUsd, 0) || 0;
  
  const totalPaidWithdrawals = allWithdrawals?.filter(w => w.status === "paid")
    .reduce((sum, w) => sum + w.amountUsd, 0) || 0;

  const totalDealValue = allDeals?.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0) || 0;
  
  const totalCommissionsDue = allDeals?.reduce((sum, d) => 
    sum + (d.commissionDueTokenUsd || 0) + (d.commissionDueFiatUsd || 0), 0) || 0;

  const tabs = [
    { id: "deals", label: "Deal Financial Management" },
    { id: "withdrawals", label: "Withdrawal Management", count: allWithdrawals?.filter(w => w.status === "in_review").length || 0 },
    { id: "reports", label: "Financial Reports" },
  ];

  return (
    <AdminLayout 
      title="Accounting Panel" 
      description="Financial management and commission tracking"
    >
      <NavigationTabs 
        tabs={tabs}
        activeTab={activeSection}
        onTabChange={setActiveSection}
      />

      {activeSection === "deals" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Total Deal Value"
              value={`$${totalDealValue.toLocaleString()}`}
              description="All deals combined"
              color="blue"
            />
            <StatsCard
              title="Commissions Due"
              value={`$${totalCommissionsDue.toLocaleString()}`}
              description="Total owed to partners"
              color="green"
            />
            <StatsCard
              title="Active Deals"
              value={allDeals?.filter(d => d.status === "in_progress").length || 0}
              description="Currently processing"
              color="yellow"
            />
            <StatsCard
              title="Commission Rate"
              value={allDeals?.length ? 
                `${Math.round((totalCommissionsDue / totalDealValue) * 100)}%` : 
                "0%"
              }
              description="Average commission"
              color="purple"
            />
          </div>

          <div className="bg-white p-6 rounded-lg border border-gray-200">
            <div className="mb-6">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="text-lg font-semibold mb-2">Deal Financial Management</h3>
                  <p className="text-gray-600">
                    Set and update monetary values, token amounts, and commission calculations for all deals.
                  </p>
                </div>
                <button
                  onClick={handleFixExistingDeals}
                  className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors text-sm"
                >
                  Fix Missing Commissions
                </button>
              </div>
            </div>
            
            <DealTable
              deals={allDeals || []}
              partners={allUsers || []}
              loading={!allDeals}
              onUpdateDeal={handleUpdateDeal}
              showFinancials={true}
              emptyMessage="No deals found. Deals are automatically created when leads are approved by operations."
            />
          </div>
        </div>
      )}

      {activeSection === "withdrawals" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Pending Requests"
              value={`$${totalPendingWithdrawals.toLocaleString()}`}
              description="Awaiting processing"
              color="yellow"
            />
            <StatsCard
              title="Total Paid"
              value={`$${totalPaidWithdrawals.toLocaleString()}`}
              description="Successfully processed"
              color="green"
            />
            <StatsCard
              title="Total Requests"
              value={allWithdrawals?.length || 0}
              description="All time"
              color="blue"
            />
            <StatsCard
              title="Approval Rate"
              value={allWithdrawals?.length ? 
                `${Math.round(((allWithdrawals.filter(w => w.status === "paid" || w.status === "approved").length) / allWithdrawals.length) * 100)}%` : 
                "0%"
              }
              description="Success percentage"
              color="purple"
            />
          </div>

          <WithdrawalTable
            withdrawals={allWithdrawals || []}
            loading={!allWithdrawals}
            onUpdateWithdrawal={handleUpdateWithdrawal}
          />
        </div>
      )}

      {activeSection === "reports" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold mb-4">Commission Overview</h3>
              {financialSummary ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Total Deal Value:</span>
                    <span className="text-lg font-bold text-blue-600">
                      ${financialSummary.deals.totalValue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Total Commissions Due:</span>
                    <span className="text-lg font-bold text-green-600">
                      ${financialSummary.deals.totalCommissionsDue.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Pending Commissions:</span>
                    <span className="text-lg font-bold text-yellow-600">
                      ${financialSummary.deals.totalCommissionsPending.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Total Deals:</span>
                    <span className="text-lg font-bold text-purple-600">
                      {financialSummary.deals.count}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
            
            <div className="bg-white p-6 rounded-lg border border-gray-200">
              <h3 className="text-lg font-semibold mb-4">Withdrawal Summary</h3>
              {financialSummary ? (
                <div className="space-y-4">
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Total Requested:</span>
                    <span className="text-lg font-bold text-blue-600">
                      ${financialSummary.withdrawals.totalRequested.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Total Paid:</span>
                    <span className="text-lg font-bold text-green-600">
                      ${financialSummary.withdrawals.totalPaid.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Pending:</span>
                    <span className="text-lg font-bold text-yellow-600">
                      ${financialSummary.withdrawals.totalPending.toLocaleString()}
                    </span>
                  </div>
                  <div className="flex justify-between items-center p-3 bg-gray-50 rounded">
                    <span className="font-medium">Total Requests:</span>
                    <span className="text-lg font-bold text-purple-600">
                      {financialSummary.withdrawals.count}
                    </span>
                  </div>
                </div>
              ) : (
                <div className="flex justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>
              )}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatsCard
              title="Cash Flow Health"
              value={totalCommissionsDue > totalPendingWithdrawals ? "Positive" : "Watch"}
              description="Commission vs Withdrawal ratio"
              color={totalCommissionsDue > totalPendingWithdrawals ? "green" : "yellow"}
            />
            <StatsCard
              title="Payment Efficiency"
              value={`${Math.round((totalPaidWithdrawals / (totalPaidWithdrawals + totalPendingWithdrawals)) * 100)}%`}
              description="Processed vs Pending"
              color="blue"
            />
            <StatsCard
              title="Average Deal Size"
              value={allDeals?.length ? 
                `$${Math.round(totalDealValue / allDeals.length).toLocaleString()}` : 
                "$0"
              }
              description="Revenue per deal"
              color="purple"
            />
          </div>
        </div>
      )}
    </AdminLayout>
  );
}