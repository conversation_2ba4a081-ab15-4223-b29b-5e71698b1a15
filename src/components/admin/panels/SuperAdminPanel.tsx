import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../../../convex/_generated/dataModel";

import { AdminLayout } from "../shared/AdminLayout";
import { NavigationTabs } from "../shared/NavigationTabs";
import { StatsCard } from "../shared/StatsCard";
import { UserTable } from "../shared/UserTable";
import { LeadTable } from "../shared/LeadTable";
import { DealTable } from "../shared/DealTable";
import { WithdrawalTable } from "../shared/WithdrawalTable";
import { AuditLogViewer } from "../shared/AuditLogViewer";

export function SuperAdminPanel() {
  const [activeSection, setActiveSection] = useState("system");

  // Data queries
  const users = useQuery(api.admin.listUsers);
  const allLeads = useQuery(api.leads.listMine, {});
  const allDeals = useQuery(api.deals.listForPartner, {});
  const allWithdrawals = useQuery(api.earnings.listWithdrawals, {});
  const auditLogs = useQuery(api.admin.getAuditLogs, { limit: 50 });
  const pendingApprovals = useQuery(api.admin.listPendingApprovals);

  // Admin script mutations
  const setAllUsersPending = useMutation(api.partnerApplications.setAllUsersPending);
  const createTestPartnerApplication = useMutation(api.partnerApplications.createTestPartnerApplication);
  
  // Mutations
  const makeAdmin = useMutation(api.admin.makeAdmin);
  const approveUser = useMutation(api.admin.approveUser);
  const updateUserTier = useMutation(api.admin.updateUserTier);
  const approveLead = useMutation(api.leads.approve);
  const updateDeal = useMutation(api.deals.update);
  const updateWithdrawalStatus = useMutation(api.earnings.updateWithdrawalStatus);

  // Form state
  const [newAdminForm, setNewAdminForm] = useState({
    email: "",
    role: "admin" as const,
  });

  // Handlers
  const handleMakeAdmin = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const targetUser = users?.find(u => u.email === newAdminForm.email);
      if (!targetUser) {
        toast.error("User not found");
        return;
      }
      await makeAdmin({ userId: targetUser._id, role: newAdminForm.role });
      toast.success(`User promoted to ${newAdminForm.role}`);
      setNewAdminForm({ email: "", role: "admin" });
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to promote user");
    }
  };

  const handleApproveUser = async (userId: Id<"users">, approved: boolean) => {
    try {
      await approveUser({ userId, approved });
      toast.success(`User ${approved ? "approved" : "rejected"}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update user");
    }
  };

  const handleUpdateTier = async (userId: Id<"users">, tier: "trusted" | "elite" | "diamond") => {
    try {
      await updateUserTier({ userId, tier });
      toast.success(`User tier updated to ${tier}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update tier");
    }
  };

  const handleApproveLead = async (leadId: Id<"leads">, approved: boolean, details?: any) => {
    try {
      await approveLead({ 
        leadId, 
        approved, 
        telegramGroupUrl: details?.telegramGroupUrl,
        dealType: details?.dealType,
        commissionPct: details?.commissionPct,
      });
      toast.success(`Lead ${approved ? "approved and deal created" : "rejected"}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update lead");
    }
  };

  const handleUpdateDeal = async (dealId: Id<"deals">, updates: any) => {
    try {
      await updateDeal({ dealId, ...updates });
      toast.success("Deal updated successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update deal");
    }
  };

  const handleUpdateWithdrawal = async (withdrawalId: Id<"withdrawals">, status: "approved" | "paid" | "rejected", txRef?: string) => {
    try {
      await updateWithdrawalStatus({ withdrawalId, status, txIdOrRef: txRef });
      toast.success(`Withdrawal ${status}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update withdrawal");
    }
  };

  // Admin script handlers
  const handleSetAllUsersPending = async () => {
    if (!confirm("⚠️ This will set ALL non-admin users as pending partner applicants. Are you sure?")) {
      return;
    }

    try {
      const result = await setAllUsersPending();
      toast.success(result.message);
      console.log("✅ Set all users pending result:", result);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to set users as pending");
      console.error("❌ Set all users pending error:", error);
    }
  };

  const handleCreateTestApplication = async () => {
    try {
      const result = await createTestPartnerApplication();
      toast.success(result.message);
      console.log("✅ Test application created:", result);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create test application");
      console.error("❌ Create test application error:", error);
    }
  };


  // Calculate stats
  const totalPendingWithdrawals = allWithdrawals?.filter(w => w.status === "in_review")
    .reduce((sum, w) => sum + w.amountUsd, 0) || 0;
  
  const totalDealValue = allDeals?.reduce((sum, d) => sum + (d.dealValueUsd || 0), 0) || 0;

  const tabs = [
    { id: "system", label: "System Overview" },
    { id: "users", label: "User Management", count: pendingApprovals?.users.length || 0 },
    { id: "operations", label: "Operations", count: pendingApprovals?.leads.length || 0 },
    { id: "financial", label: "Financial", count: pendingApprovals?.withdrawals.length || 0 },
    { id: "roles", label: "Role Management" },
    { id: "audit", label: "System Audit" },
  ];

  return (
    <AdminLayout 
      title="Super Admin Panel" 
      description="Complete system administration and oversight"
    >
      <NavigationTabs 
        tabs={tabs}
        activeTab={activeSection}
        onTabChange={setActiveSection}
      />

      {activeSection === "system" && (
        <div className="space-y-grid-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-grid-6">
            <StatsCard
              title="System Health"
              value="Operational"
              description="All systems running normally"
              color="blue"
            />
            <StatsCard
              title="Total Users"
              value={users?.length || 0}
              description="Registered partners"
              color="green"
            />
            <StatsCard
              title="Total Deal Value"
              value={`$${totalDealValue.toLocaleString()}`}
              description="All-time revenue"
              color="purple"
            />
            <StatsCard
              title="Pending Actions"
              value={(pendingApprovals?.users.length || 0) + (pendingApprovals?.leads.length || 0) + (pendingApprovals?.withdrawals.length || 0)}
              description="Requiring attention"
              color="yellow"
            />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-grid-8">
            <div className="glass-card p-grid-6 border-0">
              <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">Recent Activity</h3>
              <AuditLogViewer
                auditLogs={auditLogs || []}
                loading={!auditLogs}
                limit={5}
              />
            </div>

            <div className="glass-card p-grid-6 border-0">
              <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">Quick Stats</h3>
              <div className="space-y-grid-4">
                <div className="flex justify-between">
                  <span className="text-size-3 text-muted-foreground">Active Deals:</span>
                  <span className="text-size-3 font-semibold text-foreground">
                    {allDeals?.filter(d => d.status === "in_progress").length || 0}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span className="text-size-3 text-muted-foreground">Pending Withdrawals:</span>
                  <span className="text-size-3 font-semibold text-foreground">${totalPendingWithdrawals.toLocaleString()}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-size-3 text-muted-foreground">Total Leads:</span>
                  <span className="text-size-3 font-semibold text-foreground">{allLeads?.length || 0}</span>
                </div>
              </div>
            </div>
          </div>

          {/* Admin Scripts Section */}
          <div className="glass-card p-grid-6 border-0">
            <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">Admin Scripts</h3>
            <p className="text-size-4 text-muted-foreground mb-grid-4">
              Dangerous operations that should only be run by super admins. Use with caution.
            </p>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-grid-4">
              <div className="glass-card p-grid-4 space-y-grid-3 border-0">
                <h4 className="text-size-3 font-semibold text-foreground">Set All Users as Pending</h4>
                <p className="text-size-4 text-muted-foreground">
                  Sets all non-admin users as pending partner applicants. This will make them appear in the partner applications list for review.
                </p>
                <button
                  onClick={handleSetAllUsersPending}
                  className="btn-primary w-full"
                >
                  🔄 Set All Users Pending
                </button>
              </div>

              <div className="glass-card p-grid-4 space-y-grid-3 border-0">
                <h4 className="text-size-3 font-semibold text-foreground">Create Test Application</h4>
                <p className="text-size-4 text-muted-foreground">
                  Creates a realistic test partner <NAME_EMAIL> with complete profile data.
                </p>
                <button
                  onClick={handleCreateTestApplication}
                  className="btn-secondary w-full"
                >
                  🧪 Create Test Application
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {activeSection === "users" && (
        <div className="glass-card p-grid-6 border-0">
          <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">User Management</h3>
          <UserTable
            users={users || []}
            loading={!users}
            onApproveUser={handleApproveUser}
            onUpdateTier={handleUpdateTier}
          />
        </div>
      )}

      {activeSection === "operations" && (
        <div className="space-y-grid-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-grid-6">
            <StatsCard
              title="Total Leads"
              value={allLeads?.length || 0}
              color="blue"
            />
            <StatsCard
              title="Approved Leads"
              value={allLeads?.filter(l => l.approved === true).length || 0}
              color="green"
            />
            <StatsCard
              title="Pending Leads"
              value={allLeads?.filter(l => l.approved === undefined).length || 0}
              color="yellow"
            />
            <StatsCard
              title="Active Deals"
              value={allDeals?.filter(d => d.status === "in_progress").length || 0}
              color="purple"
            />
          </div>

          <LeadTable
            leads={allLeads || []}
            loading={!allLeads}
            onApproveLead={handleApproveLead}
          />

          <div className="glass-card p-grid-6 border-0">
            <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">All Deals</h3>
            <DealTable
              deals={allDeals || []}
              loading={!allDeals}
              onUpdateDeal={handleUpdateDeal}
              showFinancials={true}
            />
          </div>
        </div>
      )}

      {activeSection === "financial" && (
        <div className="space-y-grid-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-grid-6">
            <StatsCard
              title="Pending Withdrawals"
              value={`$${totalPendingWithdrawals.toLocaleString()}`}
              description="Awaiting approval"
              color="yellow"
            />
            <StatsCard
              title="Total Deal Value"
              value={`$${totalDealValue.toLocaleString()}`}
              description="All-time revenue"
              color="green"
            />
            <StatsCard
              title="Total Requests"
              value={allWithdrawals?.length || 0}
              description="Withdrawal requests"
              color="blue"
            />
          </div>

          <div className="glass-card p-grid-6 border-0">
            <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">Withdrawal Management</h3>
            <WithdrawalTable
              withdrawals={allWithdrawals || []}
              loading={!allWithdrawals}
              onUpdateWithdrawal={handleUpdateWithdrawal}
            />
          </div>

          <div className="glass-card p-grid-6 border-0">
            <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">Financial Deal Management</h3>
            <DealTable
              deals={allDeals || []}
              loading={!allDeals}
              onUpdateDeal={handleUpdateDeal}
              showFinancials={true}
            />
          </div>
        </div>
      )}

      {activeSection === "roles" && (
        <div className="glass-card p-grid-6 border-0">
          <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">Promote User to Admin Role</h3>
          <form onSubmit={handleMakeAdmin} className="space-y-grid-4 max-w-md">
            <div>
              <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                User Email
              </label>
              <input
                type="email"
                value={newAdminForm.email}
                onChange={(e) => setNewAdminForm({ ...newAdminForm, email: e.target.value })}
                className="auth-input-field"
                placeholder="<EMAIL>"
                required
              />
            </div>
            <div>
              <label className="block text-size-4 font-semibold text-foreground mb-grid-2">
                Role
              </label>
              <select
                value={newAdminForm.role}
                onChange={(e) => setNewAdminForm({ ...newAdminForm, role: e.target.value as any })}
                className="auth-input-field"
              >
                <option value="admin">Admin</option>
                <option value="ops">Operations</option>
                <option value="accounting">Accounting</option>
                <option value="sales">Sales</option>
              </select>
            </div>
            <button
              type="submit"
              className="btn-primary"
            >
              Promote User
            </button>
          </form>
        </div>
      )}

      {activeSection === "audit" && (
        <div className="glass-card p-grid-6 border-0">
          <h3 className="text-size-2 font-semibold mb-grid-4 text-foreground">System Audit Logs</h3>
          <AuditLogViewer
            auditLogs={auditLogs || []}
            loading={!auditLogs}
          />
        </div>
      )}
    </AdminLayout>
  );
}