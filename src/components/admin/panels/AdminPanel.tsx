import { useState } from "react";
import { useQuery, useMutation } from "convex/react";
import { api } from "../../../../convex/_generated/api";
import { toast } from "sonner";
import { Id } from "../../../../convex/_generated/dataModel";

import { AdminLayout } from "../shared/AdminLayout";
import { NavigationTabs } from "../shared/NavigationTabs";
import { StatsCard } from "../shared/StatsCard";
import { UserTable } from "../shared/UserTable";
import { AuditLogViewer } from "../shared/AuditLogViewer";

export function AdminPanel() {
  const [activeSection, setActiveSection] = useState("users");
  
  // Data queries
  const users = useQuery(api.admin.listUsers);
  const pendingApprovals = useQuery(api.admin.listPendingApprovals);
  const auditLogs = useQuery(api.admin.getAuditLogs, { limit: 25 });
  
  // Mutations
  const approveUser = useMutation(api.admin.approveUser);
  const updateUserTier = useMutation(api.admin.updateUserTier);
  
  // Handlers
  const handleApproveUser = async (userId: Id<"users">, approved: boolean) => {
    try {
      await approveUser({ userId, approved });
      toast.success(`User ${approved ? "approved" : "rejected"}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update user");
    }
  };

  const handleUpdateTier = async (userId: Id<"users">, tier: "trusted" | "elite" | "diamond") => {
    try {
      await updateUserTier({ userId, tier });
      toast.success(`User tier updated to ${tier}`);
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update tier");
    }
  };

  // Calculate stats
  const approvedUsers = users?.filter(u => u.approved).length || 0;
  const pendingUsers = users?.filter(u => !u.approved).length || 0;

  const tabs = [
    { id: "users", label: "User Management" },
    { id: "approvals", label: "Pending Approvals", count: pendingUsers },
    { id: "audit", label: "Activity Logs" },
  ];

  return (
    <AdminLayout 
      title="Admin Panel" 
      description="User management and system administration"
    >
      <NavigationTabs 
        tabs={tabs}
        activeTab={activeSection}
        onTabChange={setActiveSection}
      />

      {activeSection === "users" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <StatsCard
              title="Total Users"
              value={users?.length || 0}
              description="All registered users"
              color="blue"
            />
            <StatsCard
              title="Approved Users"
              value={approvedUsers}
              description="Active partners"
              color="green"
            />
            <StatsCard
              title="Pending Approval"
              value={pendingUsers}
              description="Awaiting review"
              color="yellow"
            />
            <StatsCard
              title="Partner Tiers"
              value="3"
              description="Trusted, Elite, Diamond"
              color="purple"
            />
          </div>

          <UserTable
            users={users || []}
            loading={!users}
            onApproveUser={handleApproveUser}
            onUpdateTier={handleUpdateTier}
          />
        </div>
      )}

      {activeSection === "approvals" && (
        <div className="space-y-8">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <StatsCard
              title="Pending Users"
              value={pendingUsers}
              description="Require approval"
              color="yellow"
            />
            <StatsCard
              title="Recent Signups"
              value={users?.filter(u => 
                new Date(u._creationTime) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
              ).length || 0}
              description="In the last 7 days"
              color="blue"
            />
          </div>

          <div 
            className="p-6 rounded-lg"
            style={{
              backgroundColor: 'var(--surface-1)',
              border: '1px solid var(--border-default)'
            }}
          >
            <h3 className="text-size-2 mb-4" style={{ color: 'var(--foreground)' }}>Pending User Approvals</h3>
            {!pendingApprovals ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2" style={{ borderColor: 'var(--accent)' }}></div>
              </div>
            ) : pendingApprovals.users.length === 0 ? (
              <p className="text-center py-8 text-size-3" style={{ color: 'var(--muted)' }}>No pending user approvals</p>
            ) : (
              <UserTable
                users={pendingApprovals.users}
                onApproveUser={handleApproveUser}
                onUpdateTier={handleUpdateTier}
                emptyMessage="No pending approvals"
              />
            )}
          </div>
        </div>
      )}

      {activeSection === "audit" && (
        <div className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <StatsCard
              title="Total Events"
              value={auditLogs?.length || 0}
              description="Recent audit entries"
              color="blue"
            />
            <StatsCard
              title="User Actions"
              value={auditLogs?.filter(log => 
                log.action.includes("USER")
              ).length || 0}
              description="User-related events"
              color="green"
            />
            <StatsCard
              title="Today's Activity"
              value={auditLogs?.filter(log => 
                new Date(log.at).toDateString() === new Date().toDateString()
              ).length || 0}
              description="Events from today"
              color="purple"
            />
          </div>

          <AuditLogViewer 
            auditLogs={auditLogs || []} 
            loading={!auditLogs}
          />
        </div>
      )}
    </AdminLayout>
  );
}