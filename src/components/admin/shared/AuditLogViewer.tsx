import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";

interface AuditLog {
  _id: Id<"auditLogs">;
  actorUserId: Id<"users">;
  action: string;
  entity: string;
  meta?: any;
  at: number;
}

interface AuditLogViewerProps {
  auditLogs: AuditLog[];
  loading?: boolean;
  limit?: number;
  emptyMessage?: string;
}

export function AuditLogViewer({ 
  auditLogs, 
  loading = false,
  limit,
  emptyMessage = "No audit logs found"
}: AuditLogViewerProps) {
  const displayLogs = limit ? auditLogs.slice(0, limit) : auditLogs;

  const getActionColor = (action: string) => {
    if (action.includes("CREATED") || action.includes("APPROVED")) return "text-green-700 bg-green-100";
    if (action.includes("DELETED") || action.includes("REJECTED")) return "text-red-700 bg-red-100";
    if (action.includes("UPDATED") || action.includes("MODIFIED")) return "text-blue-700 bg-blue-100";
    return "text-gray-700 bg-gray-100";
  };

  const formatEntity = (entity: string) => {
    const parts = entity.split('/');
    if (parts.length === 2) {
      return {
        type: parts[0],
        id: parts[1]
      };
    }
    return { type: entity, id: null };
  };

  const columns: Column<AuditLog>[] = [
    {
      key: "at",
      label: "Time",
      sortable: true,
      width: "160px",
      render: (log) => (
        <div className="text-sm">
          <div>{new Date(log.at).toLocaleDateString()}</div>
          <div className="text-gray-500">
            {new Date(log.at).toLocaleTimeString()}
          </div>
        </div>
      )
    },
    {
      key: "action",
      label: "Action",
      render: (log) => (
        <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getActionColor(log.action)}`}>
          {log.action.replace(/_/g, " ").toLowerCase()}
        </span>
      )
    },
    {
      key: "entity",
      label: "Entity",
      render: (log) => {
        const { type, id } = formatEntity(log.entity);
        return (
          <div>
            <div className="font-medium">{type}</div>
            {id && (
              <div className="text-sm text-gray-600 font-mono">
                {id.length > 12 ? `${id.slice(0, 8)}...` : id}
              </div>
            )}
          </div>
        );
      }
    },
    {
      key: "actorUserId",
      label: "Actor",
      render: (log) => (
        <div className="text-sm font-mono text-gray-600">
          {log.actorUserId.slice(0, 8)}...
        </div>
      )
    },
    {
      key: "meta",
      label: "Details",
      render: (log) => (
        log.meta ? (
          <details className="max-w-xs">
            <summary className="cursor-pointer text-blue-600 hover:text-blue-800">
              View details
            </summary>
            <pre className="mt-2 text-xs text-gray-600 bg-gray-50 p-2 rounded border max-h-32 overflow-auto">
              {JSON.stringify(log.meta, null, 2)}
            </pre>
          </details>
        ) : (
          <span className="text-gray-400">-</span>
        )
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">System Audit Logs</h3>
        {limit && auditLogs.length > limit && (
          <span className="text-sm text-gray-600">
            Showing {limit} of {auditLogs.length} logs
          </span>
        )}
      </div>
      
      <DataTable
        data={displayLogs}
        columns={columns}
        loading={loading}
        emptyMessage={emptyMessage}
      />
      
      {limit && auditLogs.length > limit && (
        <div className="text-center">
          <p className="text-sm text-gray-500">
            {auditLogs.length - limit} more logs available. 
            Increase limit to see more entries.
          </p>
        </div>
      )}
    </div>
  );
}