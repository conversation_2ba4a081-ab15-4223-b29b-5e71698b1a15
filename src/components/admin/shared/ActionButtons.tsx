interface ActionButtonsProps {
  onApprove?: () => void;
  onReject?: () => void;
  onEdit?: () => void;
  onView?: () => void;
  onDelete?: () => void;
  approveText?: string;
  rejectText?: string;
  editText?: string;
  viewText?: string;
  deleteText?: string;
  loading?: boolean;
  disabled?: boolean;
}

export function ActionButtons({
  onApprove,
  onReject,
  onEdit,
  onView,
  onDelete,
  approveText = "Approve",
  rejectText = "Reject",
  editText = "Edit",
  viewText = "View",
  deleteText = "Delete",
  loading = false,
  disabled = false,
}: ActionButtonsProps) {
  const baseClasses = "px-3 py-1 rounded text-sm font-medium transition-colors";
  const disabledClasses = "opacity-50 cursor-not-allowed";

  return (
    <div className="flex gap-2">
      {onApprove && (
        <button
          onClick={onApprove}
          disabled={loading || disabled}
          className={`${baseClasses} bg-green-600 text-white hover:bg-green-700 ${
            (loading || disabled) ? disabledClasses : ""
          }`}
        >
          {loading ? "..." : approveText}
        </button>
      )}
      
      {onReject && (
        <button
          onClick={onReject}
          disabled={loading || disabled}
          className={`${baseClasses} bg-red-600 text-white hover:bg-red-700 ${
            (loading || disabled) ? disabledClasses : ""
          }`}
        >
          {loading ? "..." : rejectText}
        </button>
      )}
      
      {onEdit && (
        <button
          onClick={onEdit}
          disabled={loading || disabled}
          className={`${baseClasses} bg-blue-600 text-white hover:bg-blue-700 ${
            (loading || disabled) ? disabledClasses : ""
          }`}
        >
          {loading ? "..." : editText}
        </button>
      )}
      
      {onView && (
        <button
          onClick={onView}
          disabled={loading || disabled}
          className={`${baseClasses} bg-gray-600 text-white hover:bg-gray-700 ${
            (loading || disabled) ? disabledClasses : ""
          }`}
        >
          {viewText}
        </button>
      )}
      
      {onDelete && (
        <button
          onClick={onDelete}
          disabled={loading || disabled}
          className={`${baseClasses} bg-red-600 text-white hover:bg-red-700 ${
            (loading || disabled) ? disabledClasses : ""
          }`}
        >
          {loading ? "..." : deleteText}
        </button>
      )}
    </div>
  );
}