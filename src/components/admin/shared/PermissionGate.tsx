import { ReactNode } from "react";

type Role = "partner" | "sales" | "ops" | "accounting" | "admin" | "superadmin";

interface PermissionGateProps {
  children: ReactNode;
  allowedRoles: Role[] | Role;
  userRoles: Role[];
  fallback?: ReactNode;
}

export function PermissionGate({ 
  children, 
  allowedRoles, 
  userRoles, 
  fallback = null 
}: PermissionGateProps) {
  const allowed = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  const hasPermission = userRoles.some(role => allowed.includes(role));
  
  return hasPermission ? <>{children}</> : <>{fallback}</>;
}

// Hook for checking permissions in components
export function useHasPermission(allowedRoles: Role[] | Role, userRoles: Role[]): boolean {
  const allowed = Array.isArray(allowedRoles) ? allowedRoles : [allowedRoles];
  return userRoles.some(role => allowed.includes(role));
}

// Helper function to get user roles from user object
export function getUserRoles(user: any): Role[] {
  // Return new roles array if it exists
  if (user.roles && user.roles.length > 0) {
    return user.roles;
  }
  
  // Fallback to single role
  if (user.role) {
    return [user.role];
  }
  
  // Default to partner
  return ["partner"];
}