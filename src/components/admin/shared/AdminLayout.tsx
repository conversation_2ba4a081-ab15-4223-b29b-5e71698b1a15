import { ReactNode } from "react";

interface AdminLayoutProps {
  children: ReactNode;
  title: string;
  description?: string;
}

export function AdminLayout({ children, title, description }: AdminLayoutProps) {
  return (
    <div className="min-h-screen" style={{ backgroundColor: 'var(--background)' }}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-size-1" style={{ color: 'var(--foreground)' }}>{title}</h1>
          {description && (
            <p className="mt-2 text-size-3" style={{ color: 'var(--muted)' }}>{description}</p>
          )}
        </div>
        <div className="space-y-8">
          {children}
        </div>
      </div>
    </div>
  );
}