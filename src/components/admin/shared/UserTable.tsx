import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { ActionButtons } from "./ActionButtons";

interface User {
  _id: Id<"users">;
  email?: string;
  name?: string;
  role?: string;
  roles?: string[];
  tier?: "trusted" | "elite" | "diamond";
  approved?: boolean;
  _creationTime: number;
}

interface UserTableProps {
  users: User[];
  loading?: boolean;
  onApproveUser?: (userId: Id<"users">, approved: boolean) => void;
  onUpdateTier?: (userId: Id<"users">, tier: "trusted" | "elite" | "diamond") => void;
  showActions?: boolean;
  emptyMessage?: string;
}

export function UserTable({ 
  users, 
  loading = false,
  onApproveUser,
  onUpdateTier,
  showActions = true,
  emptyMessage = "No users found"
}: UserTableProps) {
  const getUserRoles = (user: User) => {
    if (user.roles && user.roles.length > 0) {
      return user.roles.join(", ");
    }
    return user.role || "partner";
  };

  const columns: Column<User>[] = [
    {
      key: "email",
      label: "Email",
      sortable: true,
      render: (user) => user.email || "-"
    },
    {
      key: "name",
      label: "Name",
      sortable: true,
      render: (user) => user.name || "-"
    },
    {
      key: "role",
      label: "Roles",
      render: (user) => (
        <span 
          className="px-2 py-1 rounded text-size-4"
          style={{
            backgroundColor: 'var(--status-info-bg)',
            color: 'var(--status-info)'
          }}
        >
          {getUserRoles(user)}
        </span>
      )
    },
    {
      key: "tier",
      label: "Tier",
      render: (user) => (
        <select
          value={user.tier || "trusted"}
          onChange={(e) => onUpdateTier && onUpdateTier(user._id, e.target.value as "trusted" | "elite" | "diamond")}
          disabled={!onUpdateTier}
          className="px-2 py-1 rounded text-size-4 disabled:opacity-50"
          style={{
            border: '1px solid var(--border-default)',
            backgroundColor: 'var(--surface-2)',
            color: 'var(--foreground)'
          }}
        >
          <option value="trusted">Trusted</option>
          <option value="elite">Elite</option>
          <option value="diamond">Diamond</option>
        </select>
      )
    },
    {
      key: "approved",
      label: "Status",
      render: (user) => (
        <StatusBadge 
          status={user.approved ? "approved" : "pending"} 
          variant="user-approval"
        />
      )
    },
    {
      key: "_creationTime",
      label: "Created",
      sortable: true,
      render: (user) => new Date(user._creationTime).toLocaleDateString()
    }
  ];

  // Add actions column if showActions is true and there are action handlers
  if (showActions && onApproveUser) {
    columns.push({
      key: "actions",
      label: "Actions",
      render: (user) => (
        !user.approved ? (
          <ActionButtons
            onApprove={() => onApproveUser(user._id, true)}
            onReject={() => onApproveUser(user._id, false)}
          />
        ) : null
      )
    });
  }

  return (
    <DataTable
      data={users}
      columns={columns}
      loading={loading}
      emptyMessage={emptyMessage}
    />
  );
}