interface StatsCardProps {
  title: string;
  value: string | number;
  description?: string;
  color?: "blue" | "green" | "yellow" | "purple" | "red";
  loading?: boolean;
}

// Using design system status colors instead of hardcoded Tailwind colors
const colorStyles = {
  blue: {
    accent: "var(--status-info)",
    background: "var(--status-info-bg)",
  },
  green: {
    accent: "var(--status-success)",
    background: "var(--status-success-bg)",
  },
  yellow: {
    accent: "var(--status-warning)",
    background: "var(--status-warning-bg)",
  },
  purple: {
    accent: "var(--accent)",
    background: "oklch(from var(--accent) l c h / 0.2)",
  },
  red: {
    accent: "var(--status-error)",
    background: "var(--status-error-bg)",
  },
};

export function StatsCard({
  title,
  value,
  description,
  color = "blue",
  loading = false
}: StatsCardProps) {
  if (loading) {
    return (
      <div className="glass-card p-grid-6 animate-pulse border-0">
        <div className="h-4 rounded w-3/4 mb-grid-2" style={{ backgroundColor: 'var(--surface-2)' }}></div>
        <div className="h-8 rounded w-1/2 mb-grid-2" style={{ backgroundColor: 'var(--surface-2)' }}></div>
        <div className="h-3 rounded w-full" style={{ backgroundColor: 'var(--surface-2)' }}></div>
      </div>
    );
  }

  const formattedValue = typeof value === 'number' ? value.toLocaleString() : value;
  const colorStyle = colorStyles[color];

  return (
    <div className="metric-card p-grid-6 border-0 group">
      <div className="space-y-grid-3">
        <div className="flex items-center justify-between">
          <h3 className="text-size-4 font-semibold text-muted-foreground">{title}</h3>
          <div
            className="w-2 h-2 rounded-full"
            style={{ backgroundColor: colorStyle.accent }}
          />
        </div>

        <div className="space-y-grid-1">
          <p
            className="text-size-1 font-semibold group-hover:scale-105 transition-transform duration-200"
            style={{ color: colorStyle.accent }}
          >
            {formattedValue}
          </p>
          {description && (
            <p className="text-size-4 text-muted-foreground">
              {description}
            </p>
          )}
        </div>
      </div>
    </div>
  );
}