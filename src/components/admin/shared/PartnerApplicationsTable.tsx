import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { Button } from "../../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../ui/select";
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../../ui/dialog";
import { Label } from "../../ui/label";
import { Textarea } from "../../ui/textarea";
import { toast } from "sonner";

interface PartnerApplication {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
  email?: string;
  companyName?: string;
  companyType?: string;
  roleTitle?: string;
  telegram?: string;
  whatsapp?: string;
  xProfile?: string;
  preferredCommunication?: string[];
  profileCompleted?: boolean;
  _creationTime: number;
}

interface SalesMember {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
}

interface PartnerApplicationsTableProps {
  applications: PartnerApplication[];
  salesMembers: SalesMember[];
  loading?: boolean;
  onReviewApplication?: (
    partnerId: Id<"users">, 
    approved: boolean, 
    tier?: "trusted" | "elite" | "diamond",
    salesUserId?: Id<"users">,
    notes?: string
  ) => void;
}

export function PartnerApplicationsTable({ 
  applications, 
  salesMembers,
  loading = false,
  onReviewApplication
}: PartnerApplicationsTableProps) {
  const [reviewingApplication, setReviewingApplication] = useState<PartnerApplication | null>(null);
  const [reviewData, setReviewData] = useState({
    approved: true,
    tier: "trusted" as "trusted" | "elite" | "diamond",
    salesUserId: "unassigned",
    notes: ""
  });

  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!onReviewApplication || !reviewingApplication) return;

    try {
      await onReviewApplication(
        reviewingApplication._id,
        reviewData.approved,
        reviewData.approved ? reviewData.tier : undefined,
        reviewData.approved && reviewData.salesUserId !== "unassigned" ? reviewData.salesUserId as Id<"users"> : undefined,
        reviewData.notes || undefined
      );
      setReviewingApplication(null);
      setReviewData({ approved: true, tier: "trusted", salesUserId: "unassigned", notes: "" });
      toast.success(`Application ${reviewData.approved ? "approved" : "rejected"} successfully`);
    } catch (error) {
      toast.error("Failed to review application");
    }
  };

  const handleReview = (application: PartnerApplication) => {
    setReviewingApplication(application);
    setReviewData({ approved: true, tier: "trusted", salesUserId: "unassigned", notes: "" });
  };

  const handleQuickApprove = async (partnerId: Id<"users">) => {
    if (!onReviewApplication) return;
    
    try {
      await onReviewApplication(partnerId, true, "trusted");
      toast.success("Application approved successfully");
    } catch (error) {
      toast.error("Failed to approve application");
    }
  };

  const handleQuickReject = async (partnerId: Id<"users">) => {
    if (!onReviewApplication) return;
    
    if (confirm("Are you sure you want to reject this application?")) {
      try {
        await onReviewApplication(partnerId, false);
        toast.success("Application rejected successfully");
      } catch (error) {
        toast.error("Failed to reject application");
      }
    }
  };

  const columns: Column<PartnerApplication>[] = [
    {
      key: "name",
      label: "Name",
      sortable: true,
      render: (app) => app.name || app.fullName || "-"
    },
    {
      key: "email",
      label: "Email",
      sortable: true,
      render: (app) => app.email || "-"
    },
    {
      key: "companyName",
      label: "Company",
      sortable: true,
      render: (app) => app.companyName || "-"
    },
    {
      key: "companyType",
      label: "Company Type",
      render: (app) => app.companyType || "-"
    },
    {
      key: "roleTitle",
      label: "Role",
      render: (app) => app.roleTitle || "-"
    },
    {
      key: "profileCompleted",
      label: "Profile Status",
      render: (app) => (
        <StatusBadge 
          status={app.profileCompleted ? "complete" : "incomplete"}
          variant={app.profileCompleted ? "success" : "warning"}
        />
      )
    },
    {
      key: "_creationTime",
      label: "Applied",
      sortable: true,
      render: (app) => new Date(app._creationTime).toLocaleDateString()
    },
    {
      key: "actions",
      label: "Actions",
      render: (app) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleReview(app)}
          >
            Review
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickApprove(app._id)}
            className="text-green-600 hover:text-green-800"
          >
            Quick Approve
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleQuickReject(app._id)}
            className="text-red-600 hover:text-red-800"
          >
            Reject
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Partner Applications</h3>
        <div className="text-sm text-gray-600">
          {applications.length} pending applications
        </div>
      </div>

      {/* Review Dialog */}
      <Dialog open={!!reviewingApplication} onOpenChange={() => setReviewingApplication(null)}>
        <DialogContent className="glass-card border-0 max-w-3xl">
          <DialogHeader className="pb-grid-6">
            <DialogTitle className="text-size-2 text-foreground">Review Partner Application</DialogTitle>
          </DialogHeader>

          {reviewingApplication && (
            <div className="space-y-grid-6">
              {/* Application Details */}
              <div className="glass-card p-grid-4 space-y-grid-4">
                <h4 className="text-size-3 font-semibold text-foreground">Application Details</h4>
                <div className="grid grid-cols-2 gap-grid-4 text-size-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.name || reviewingApplication.fullName || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.email || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Company:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.companyName || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Company Type:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.companyType || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Role:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.roleTitle || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Telegram:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.telegram || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">WhatsApp:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.whatsapp || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">X Profile:</span>
                    <span className="text-foreground font-medium">
                      {reviewingApplication.xProfile || "Not provided"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Review Form */}
              <form onSubmit={handleReviewSubmit} className="space-y-grid-6">
                <div className="space-y-grid-2">
                  <Label className="text-size-4 font-semibold text-foreground">Decision</Label>
                  <Select
                    value={reviewData.approved ? "approve" : "reject"}
                    onValueChange={(value) => setReviewData({ ...reviewData, approved: value === "approve" })}
                  >
                    <SelectTrigger className="auth-input-field">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-card border-0">
                      <SelectItem value="approve" className="text-size-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-400"></div>
                          Approve Application
                        </div>
                      </SelectItem>
                      <SelectItem value="reject" className="text-size-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-red-400"></div>
                          Reject Application
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {reviewData.approved && (
                  <div className="space-y-grid-4">
                    <div className="space-y-grid-2">
                      <Label className="text-size-4 font-semibold text-foreground">Partner Tier</Label>
                      <Select
                        value={reviewData.tier}
                        onValueChange={(value) => setReviewData({ ...reviewData, tier: value as "trusted" | "elite" | "diamond" })}
                      >
                        <SelectTrigger className="auth-input-field">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent className="glass-card border-0">
                          <SelectItem value="trusted" className="text-size-4">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                              Trusted (5% commission)
                            </div>
                          </SelectItem>
                          <SelectItem value="elite" className="text-size-4">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                              Elite (7.5% commission)
                            </div>
                          </SelectItem>
                          <SelectItem value="diamond" className="text-size-4">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-green-400"></div>
                              Diamond (10% commission)
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-grid-2">
                      <Label className="text-size-4 font-semibold text-foreground">Sales Assignment (optional)</Label>
                      <Select
                        value={reviewData.salesUserId}
                        onValueChange={(value) => setReviewData({ ...reviewData, salesUserId: value })}
                      >
                        <SelectTrigger className="auth-input-field">
                          <SelectValue placeholder="Select a salesperson" />
                        </SelectTrigger>
                        <SelectContent className="glass-card border-0">
                          <SelectItem value="unassigned" className="text-size-4">
                            <div className="flex items-center gap-2">
                              <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                              No assignment
                            </div>
                          </SelectItem>
                          {salesMembers.map((member) => (
                            <SelectItem key={member._id} value={member._id} className="text-size-4">
                              <div className="flex items-center gap-2">
                                <div className="w-2 h-2 rounded-full bg-accent"></div>
                                {member.name || member.fullName || "Unknown"}
                              </div>
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                )}

                <div className="space-y-grid-2">
                  <Label className="text-size-4 font-semibold text-foreground">Notes (optional)</Label>
                  <Textarea
                    value={reviewData.notes}
                    onChange={(e) => setReviewData({ ...reviewData, notes: e.target.value })}
                    placeholder="Add any notes about this decision..."
                    rows={3}
                    className="auth-input-field min-h-[80px]"
                  />
                </div>

                <div className="flex justify-end gap-grid-3 pt-grid-4 border-t border-border">
                  <Button
                    type="button"
                    variant="outline"
                    onClick={() => setReviewingApplication(null)}
                    className="btn-secondary"
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    className={reviewData.approved ? "btn-primary" : "bg-destructive hover:bg-destructive/90"}
                  >
                    {reviewData.approved ? "Approve" : "Reject"} Application
                  </Button>
                </div>
              </form>
            </div>
          )}
        </DialogContent>
      </Dialog>

      {applications.length === 0 && !loading ? (
        <div className="glass-card p-grid-8 border-0 text-center">
          <div className="space-y-grid-4">
            <div className="text-size-1 text-muted-foreground">📋</div>
            <div>
              <h4 className="text-size-2 font-semibold text-foreground mb-grid-2">No Pending Applications</h4>
              <p className="text-size-4 text-muted-foreground mb-grid-4">
                Partner applications will appear here when users complete their registration profile and are awaiting approval.
              </p>
              <div className="text-size-4 text-muted-foreground space-y-grid-2">
                <p>• Partners sign up and complete their profile</p>
                <p>• Applications are automatically flagged for ops review</p>
                <p>• Use the Super Admin panel to create test applications</p>
              </div>
            </div>
          </div>
        </div>
      ) : (
        <DataTable
          data={applications}
          columns={columns}
          loading={loading}
          emptyMessage="No pending applications found"
        />
      )}
    </div>
  );
}
