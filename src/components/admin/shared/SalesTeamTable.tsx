import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { Button } from "../../ui/button";
import { Input } from "../../ui/input";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../../ui/dialog";
import { Label } from "../../ui/label";
import { toast } from "sonner";

interface SalesMember {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
  email?: string;
  telegram?: string;
  approved?: boolean;
  _creationTime: number;
}

interface SalesTeamTableProps {
  salesMembers: SalesMember[];
  loading?: boolean;
  onCreateMember?: (data: { name: string; email: string; telegram?: string }) => void;
  onUpdateMember?: (userId: Id<"users">, data: { name?: string; email?: string; telegram?: string }) => void;
  onRemoveMember?: (userId: Id<"users">) => void;
}

export function SalesTeamTable({ 
  salesMembers, 
  loading = false,
  onCreateMember,
  onUpdateMember,
  onRemoveMember
}: SalesTeamTableProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingMember, setEditingMember] = useState<SalesMember | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    telegram: ""
  });

  const handleCreateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!onCreateMember) return;

    try {
      await onCreateMember({
        name: formData.name,
        email: formData.email,
        telegram: formData.telegram || undefined
      });
      setFormData({ name: "", email: "", telegram: "" });
      setIsCreateDialogOpen(false);
      toast.success("Sales member created successfully");
    } catch (error) {
      toast.error("Failed to create sales member");
    }
  };

  const handleUpdateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!onUpdateMember || !editingMember) return;

    try {
      await onUpdateMember(editingMember._id, {
        name: formData.name,
        email: formData.email,
        telegram: formData.telegram || undefined
      });
      setEditingMember(null);
      setFormData({ name: "", email: "", telegram: "" });
      toast.success("Sales member updated successfully");
    } catch (error) {
      toast.error("Failed to update sales member");
    }
  };

  const handleEdit = (member: SalesMember) => {
    setEditingMember(member);
    setFormData({
      name: member.name || member.fullName || "",
      email: member.email || "",
      telegram: member.telegram || ""
    });
  };

  const handleRemove = async (userId: Id<"users">) => {
    if (!onRemoveMember) return;
    
    if (confirm("Are you sure you want to remove this sales member?")) {
      try {
        await onRemoveMember(userId);
        toast.success("Sales member removed successfully");
      } catch (error) {
        toast.error("Failed to remove sales member");
      }
    }
  };

  const columns: Column<SalesMember>[] = [
    {
      key: "name",
      label: "Name",
      sortable: true,
      render: (member) => member.name || member.fullName || "-"
    },
    {
      key: "email",
      label: "Email",
      sortable: true,
      render: (member) => member.email || "-"
    },
    {
      key: "telegram",
      label: "Telegram",
      render: (member) => member.telegram ? (
        <a 
          href={`https://t.me/${member.telegram.replace('@', '')}`}
          target="_blank"
          rel="noopener noreferrer"
          className="text-blue-600 hover:text-blue-800"
        >
          {member.telegram}
        </a>
      ) : "-"
    },
    {
      key: "approved",
      label: "Status",
      render: (member) => (
        <StatusBadge 
          status={member.approved ? "approved" : "pending"}
          variant={member.approved ? "success" : "warning"}
        />
      )
    },
    {
      key: "_creationTime",
      label: "Created",
      sortable: true,
      render: (member) => new Date(member._creationTime).toLocaleDateString()
    },
    {
      key: "actions",
      label: "Actions",
      render: (member) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(member)}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleRemove(member._id)}
            className="text-red-600 hover:text-red-800"
          >
            Remove
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Sales Team Management</h3>
        <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>Add Sales Member</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Add New Sales Member</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleCreateSubmit} className="space-y-4">
              <div>
                <Label htmlFor="name">Name</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  required
                />
              </div>
              <div>
                <Label htmlFor="telegram">Telegram (optional)</Label>
                <Input
                  id="telegram"
                  value={formData.telegram}
                  onChange={(e) => setFormData({ ...formData, telegram: e.target.value })}
                  placeholder="@username"
                />
              </div>
              <div className="flex justify-end gap-2">
                <Button type="button" variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
                  Cancel
                </Button>
                <Button type="submit">Create</Button>
              </div>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingMember} onOpenChange={() => setEditingMember(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Edit Sales Member</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleUpdateSubmit} className="space-y-4">
            <div>
              <Label htmlFor="edit-name">Name</Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="edit-email">Email</Label>
              <Input
                id="edit-email"
                type="email"
                value={formData.email}
                onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                required
              />
            </div>
            <div>
              <Label htmlFor="edit-telegram">Telegram (optional)</Label>
              <Input
                id="edit-telegram"
                value={formData.telegram}
                onChange={(e) => setFormData({ ...formData, telegram: e.target.value })}
                placeholder="@username"
              />
            </div>
            <div className="flex justify-end gap-2">
              <Button type="button" variant="outline" onClick={() => setEditingMember(null)}>
                Cancel
              </Button>
              <Button type="submit">Update</Button>
            </div>
          </form>
        </DialogContent>
      </Dialog>

      <DataTable
        data={salesMembers}
        columns={columns}
        loading={loading}
        emptyMessage="No sales team members found"
      />
    </div>
  );
}
