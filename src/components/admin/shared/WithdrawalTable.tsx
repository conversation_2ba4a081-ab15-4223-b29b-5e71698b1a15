import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { ActionButtons } from "./ActionButtons";

interface Withdrawal {
  _id: Id<"withdrawals">;
  partnerId: Id<"users">;
  amountUsd: number;
  method: "usdt" | "bank";
  walletAddress?: string;
  bankDetails?: string;
  status: "in_review" | "approved" | "paid" | "rejected";
  txIdOrRef?: string;
  _creationTime: number;
}

interface WithdrawalTableProps {
  withdrawals: Withdrawal[];
  loading?: boolean;
  onUpdateWithdrawal?: (withdrawalId: Id<"withdrawals">, status: "approved" | "paid" | "rejected", txRef?: string) => void;
  showActions?: boolean;
  emptyMessage?: string;
}

export function WithdrawalTable({ 
  withdrawals, 
  loading = false,
  onUpdateWithdrawal,
  showActions = true,
  emptyMessage = "No withdrawal requests found"
}: WithdrawalTableProps) {
  const [txRef, setTxRef] = useState("");
  const [selectedWithdrawal, setSelectedWithdrawal] = useState<Id<"withdrawals"> | null>(null);

  const handleUpdateWithdrawal = async (
    withdrawalId: Id<"withdrawals">, 
    status: "approved" | "paid" | "rejected"
  ) => {
    if (!onUpdateWithdrawal) return;
    
    if (status === "paid" && selectedWithdrawal === withdrawalId) {
      onUpdateWithdrawal(withdrawalId, status, txRef);
      setTxRef("");
      setSelectedWithdrawal(null);
    } else {
      onUpdateWithdrawal(withdrawalId, status);
    }
  };

  const columns: Column<Withdrawal>[] = [
    {
      key: "amountUsd",
      label: "Amount",
      sortable: true,
      render: (withdrawal) => (
        <div className="font-medium">
          ${withdrawal.amountUsd.toLocaleString()}
        </div>
      )
    },
    {
      key: "method",
      label: "Method",
      render: (withdrawal) => (
        <div>
          <div className="font-medium capitalize">{withdrawal.method}</div>
          <div className="text-sm text-gray-600">
            {withdrawal.method === "usdt" && withdrawal.walletAddress && (
              <span title={withdrawal.walletAddress}>
                {withdrawal.walletAddress.slice(0, 10)}...{withdrawal.walletAddress.slice(-6)}
              </span>
            )}
            {withdrawal.method === "bank" && withdrawal.bankDetails && (
              <span>{withdrawal.bankDetails}</span>
            )}
          </div>
        </div>
      )
    },
    {
      key: "status",
      label: "Status",
      render: (withdrawal) => (
        <StatusBadge status={withdrawal.status} variant="withdrawal-status" />
      )
    },
    {
      key: "txIdOrRef",
      label: "Transaction/Ref",
      render: (withdrawal) => (
        withdrawal.txIdOrRef ? (
          <div className="text-sm font-mono bg-gray-100 p-1 rounded">
            {withdrawal.txIdOrRef.length > 20 
              ? `${withdrawal.txIdOrRef.slice(0, 10)}...${withdrawal.txIdOrRef.slice(-6)}`
              : withdrawal.txIdOrRef
            }
          </div>
        ) : "-"
      )
    },
    {
      key: "_creationTime",
      label: "Requested",
      sortable: true,
      render: (withdrawal) => new Date(withdrawal._creationTime).toLocaleDateString()
    }
  ];

  // Add actions column if showActions is true
  if (showActions && onUpdateWithdrawal) {
    columns.push({
      key: "actions",
      label: "Actions",
      render: (withdrawal) => {
        if (withdrawal.status === "in_review") {
          return (
            <ActionButtons
              onApprove={() => handleUpdateWithdrawal(withdrawal._id, "approved")}
              onReject={() => handleUpdateWithdrawal(withdrawal._id, "rejected")}
            />
          );
        }
        
        if (withdrawal.status === "approved") {
          return (
            <div className="space-y-2">
              {selectedWithdrawal === withdrawal._id && (
                <div className="bg-gray-50 p-3 rounded-lg">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Transaction ID or Reference
                  </label>
                  <input
                    type="text"
                    value={txRef}
                    onChange={(e) => setTxRef(e.target.value)}
                    placeholder="Transaction ID or Reference"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                  />
                </div>
              )}
              <ActionButtons
                onApprove={() => {
                  if (selectedWithdrawal === withdrawal._id) {
                    handleUpdateWithdrawal(withdrawal._id, "paid");
                  } else {
                    setSelectedWithdrawal(withdrawal._id);
                  }
                }}
                approveText={
                  selectedWithdrawal === withdrawal._id 
                    ? "Confirm Payment" 
                    : "Mark as Paid"
                }
              />
            </div>
          );
        }
        
        return null;
      }
    });
  }

  return (
    <DataTable
      data={withdrawals}
      columns={columns}
      loading={loading}
      emptyMessage={emptyMessage}
    />
  );
}