import { useState } from "react";
import { Id } from "../../../../convex/_generated/dataModel";
import { DataTable, Column } from "./DataTable";
import { StatusBadge } from "./StatusBadge";
import { Button } from "../../ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../../ui/dialog";
import { Label } from "../../ui/label";
import { toast } from "sonner";

interface Partner {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
  email?: string;
  companyName?: string;
  tier?: "trusted" | "elite" | "diamond";
  assignedSalesId?: string;
  assignedSalesName?: string;
  _creationTime: number;
}

interface SalesMember {
  _id: Id<"users">;
  name?: string;
  fullName?: string;
}

interface PartnerTableProps {
  partners: Partner[];
  salesMembers: SalesMember[];
  loading?: boolean;
  onUpdateTier?: (partnerId: Id<"users">, tier: "trusted" | "elite" | "diamond") => void;
  onAssignSales?: (partnerId: Id<"users">, salesUserId: Id<"users">) => void;
  onRemovePartner?: (partnerId: Id<"users">) => void;
}

export function PartnerTable({ 
  partners, 
  salesMembers,
  loading = false,
  onUpdateTier,
  onAssignSales,
  onRemovePartner
}: PartnerTableProps) {
  const [editingPartner, setEditingPartner] = useState<Partner | null>(null);
  const [editData, setEditData] = useState({
    tier: "trusted" as "trusted" | "elite" | "diamond",
    salesUserId: ""
  });

  const handleEdit = (partner: Partner) => {
    setEditingPartner(partner);
    setEditData({
      tier: partner.tier || "trusted",
      salesUserId: partner.assignedSalesId || "unassigned"
    });
  };

  const handleUpdateSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!editingPartner) return;

    try {
      // Update tier if changed
      if (editData.tier !== editingPartner.tier && onUpdateTier) {
        await onUpdateTier(editingPartner._id, editData.tier);
      }

      // Update sales assignment if changed
      const currentAssignment = editingPartner.assignedSalesId || "unassigned";
      if (editData.salesUserId !== currentAssignment && onAssignSales) {
        if (editData.salesUserId === "unassigned") {
          // Handle unassignment - we'll need a separate mutation for this
          console.log("TODO: Handle unassignment");
        } else {
          await onAssignSales(editingPartner._id, editData.salesUserId as Id<"users">);
        }
      }

      setEditingPartner(null);
      toast.success("Partner updated successfully");
    } catch (error) {
      toast.error("Failed to update partner");
    }
  };

  const handleRemove = async (partnerId: Id<"users">) => {
    if (!onRemovePartner) return;
    
    if (confirm("Are you sure you want to remove this partner? This action cannot be undone.")) {
      try {
        await onRemovePartner(partnerId);
        toast.success("Partner removed successfully");
      } catch (error) {
        toast.error("Failed to remove partner");
      }
    }
  };

  const getTierColor = (tier?: string) => {
    switch (tier) {
      case "diamond": return "text-purple-600";
      case "elite": return "text-blue-600";
      case "trusted": return "text-green-600";
      default: return "text-gray-600";
    }
  };

  const columns: Column<Partner>[] = [
    {
      key: "name",
      label: "Name",
      sortable: true,
      render: (partner) => partner.name || partner.fullName || "-"
    },
    {
      key: "email",
      label: "Email",
      sortable: true,
      render: (partner) => partner.email || "-"
    },
    {
      key: "companyName",
      label: "Company",
      sortable: true,
      render: (partner) => partner.companyName || "-"
    },
    {
      key: "tier",
      label: "Tier",
      sortable: true,
      render: (partner) => (
        <span className={`font-medium capitalize ${getTierColor(partner.tier)}`}>
          {partner.tier || "trusted"}
        </span>
      )
    },
    {
      key: "assignedSalesName",
      label: "Assigned Sales",
      render: (partner) => partner.assignedSalesName ? (
        <StatusBadge 
          status={partner.assignedSalesName}
          variant="info"
        />
      ) : (
        <span className="text-gray-500">Unassigned</span>
      )
    },
    {
      key: "_creationTime",
      label: "Joined",
      sortable: true,
      render: (partner) => new Date(partner._creationTime).toLocaleDateString()
    },
    {
      key: "actions",
      label: "Actions",
      render: (partner) => (
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleEdit(partner)}
          >
            Edit
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handleRemove(partner._id)}
            className="text-red-600 hover:text-red-800"
          >
            Remove
          </Button>
        </div>
      )
    }
  ];

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h3 className="text-lg font-semibold">Partner Management</h3>
        <div className="text-sm text-gray-600">
          {partners.length} approved partners
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={!!editingPartner} onOpenChange={() => setEditingPartner(null)}>
        <DialogContent className="glass-card border-0 max-w-2xl">
          <DialogHeader className="pb-grid-6">
            <DialogTitle className="text-size-2 text-foreground">Edit Partner</DialogTitle>
          </DialogHeader>
          {editingPartner && (
            <form onSubmit={handleUpdateSubmit} className="space-y-grid-6">
              {/* Partner Information Card */}
              <div className="glass-card p-grid-4 space-y-grid-3">
                <h3 className="text-size-3 font-semibold text-foreground">Partner Information</h3>
                <div className="grid grid-cols-1 gap-grid-2 text-size-4">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Name:</span>
                    <span className="text-foreground font-medium">
                      {editingPartner.name || editingPartner.fullName || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Email:</span>
                    <span className="text-foreground font-medium">
                      {editingPartner.email || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Company:</span>
                    <span className="text-foreground font-medium">
                      {editingPartner.companyName || "Not provided"}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Current Tier:</span>
                    <StatusBadge
                      status={editingPartner.tier || "trusted"}
                      variant={
                        editingPartner.tier === "diamond" ? "success" :
                        editingPartner.tier === "elite" ? "warning" : "info"
                      }
                    />
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Sales Assignment:</span>
                    <span className="text-foreground font-medium">
                      {editingPartner.assignedSalesName || "Unassigned"}
                    </span>
                  </div>
                </div>
              </div>

              {/* Edit Form */}
              <div className="space-y-grid-4">
                <div className="space-y-grid-2">
                  <Label htmlFor="tier" className="text-size-4 font-semibold text-foreground">
                    Partner Tier
                  </Label>
                  <Select value={editData.tier} onValueChange={(value) => setEditData({ ...editData, tier: value as "trusted" | "elite" | "diamond" })}>
                    <SelectTrigger className="auth-input-field">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent className="glass-card border-0">
                      <SelectItem value="trusted" className="text-size-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-blue-400"></div>
                          Trusted Partner
                        </div>
                      </SelectItem>
                      <SelectItem value="elite" className="text-size-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-yellow-400"></div>
                          Elite Partner
                        </div>
                      </SelectItem>
                      <SelectItem value="diamond" className="text-size-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-green-400"></div>
                          Diamond Partner
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-grid-2">
                  <Label htmlFor="salesUserId" className="text-size-4 font-semibold text-foreground">
                    Sales Assignment
                  </Label>
                  <Select value={editData.salesUserId} onValueChange={(value) => setEditData({ ...editData, salesUserId: value })}>
                    <SelectTrigger className="auth-input-field">
                      <SelectValue placeholder="Select sales member" />
                    </SelectTrigger>
                    <SelectContent className="glass-card border-0">
                      <SelectItem value="unassigned" className="text-size-4">
                        <div className="flex items-center gap-2">
                          <div className="w-2 h-2 rounded-full bg-gray-400"></div>
                          Unassigned
                        </div>
                      </SelectItem>
                      {salesMembers.map((member) => (
                        <SelectItem key={member._id} value={member._id} className="text-size-4">
                          <div className="flex items-center gap-2">
                            <div className="w-2 h-2 rounded-full bg-accent"></div>
                            {member.name || member.fullName || member._id}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex justify-end gap-grid-3 pt-grid-4 border-t border-border">
                <Button
                  type="button"
                  variant="outline"
                  onClick={() => setEditingPartner(null)}
                  className="btn-secondary"
                >
                  Cancel
                </Button>
                <Button
                  type="submit"
                  className="btn-primary"
                >
                  Update Partner
                </Button>
              </div>
            </form>
          )}
        </DialogContent>
      </Dialog>

      <DataTable
        data={partners}
        columns={columns}
        loading={loading}
        emptyMessage="No approved partners found"
      />
    </div>
  );
}
