interface Tab {
  id: string;
  label: string;
  count?: number;
}

interface NavigationTabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

export function NavigationTabs({ tabs, activeTab, onTabChange }: NavigationTabsProps) {
  return (
    <nav className="flex space-x-1 rounded-lg p-1" style={{ backgroundColor: 'var(--surface-2)' }}>
      {tabs.map((tab) => (
        <button
          key={tab.id}
          onClick={() => onTabChange(tab.id)}
          className={`px-4 py-3 rounded-md text-size-3 font-semibold transition-colors flex items-center gap-2 ${
            activeTab === tab.id
              ? "shadow-sm"
              : "hover:shadow-sm"
          }`}
          style={{
            backgroundColor: activeTab === tab.id ? 'var(--accent)' : 'transparent',
            color: activeTab === tab.id ? 'var(--accent-foreground)' : 'var(--muted)',
          }}
          onMouseEnter={(e) => {
            if (activeTab !== tab.id) {
              e.currentTarget.style.backgroundColor = 'var(--surface-3)';
              e.currentTarget.style.color = 'var(--foreground)';
            }
          }}
          onMouseLeave={(e) => {
            if (activeTab !== tab.id) {
              e.currentTarget.style.backgroundColor = 'transparent';
              e.currentTarget.style.color = 'var(--muted)';
            }
          }}
        >
          {tab.label}
          {tab.count !== undefined && (
            <span 
              className="px-2 py-1 rounded-full text-size-4"
              style={{
                backgroundColor: activeTab === tab.id ? 'var(--accent-foreground)' : 'var(--surface-1)',
                color: activeTab === tab.id ? 'var(--accent)' : 'var(--foreground)',
              }}
            >
              {tab.count}
            </span>
          )}
        </button>
      ))}
    </nav>
  );
}