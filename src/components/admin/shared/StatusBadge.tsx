interface StatusBadgeProps {
  status: string;
  variant?: "default" | "user-approval" | "lead-approval" | "deal-status" | "withdrawal-status";
}

const getStatusClasses = (status: string, variant: string) => {
  const lowercaseStatus = status.toLowerCase();
  
  switch (variant) {
    case "user-approval":
      switch (lowercaseStatus) {
        case "approved":
        case "true":
          return "bg-green-100 text-green-800";
        case "pending":
        case "false":
        case "rejected":
          return "bg-yellow-100 text-yellow-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
      
    case "lead-approval":
      switch (lowercaseStatus) {
        case "approved":
        case "true":
          return "bg-green-100 text-green-800";
        case "rejected":
        case "false":
          return "bg-red-100 text-red-800";
        case "pending":
        default:
          return "bg-yellow-100 text-yellow-800";
      }
      
    case "deal-status":
      switch (lowercaseStatus) {
        case "closed":
        case "paid":
          return "bg-green-100 text-green-800";
        case "in_progress":
          return "bg-blue-100 text-blue-800";
        case "lost":
          return "bg-red-100 text-red-800";
        default:
          return "bg-gray-100 text-gray-800";
      }
      
    case "withdrawal-status":
      switch (lowercaseStatus) {
        case "paid":
          return "bg-green-100 text-green-800";
        case "approved":
          return "bg-blue-100 text-blue-800";
        case "rejected":
          return "bg-red-100 text-red-800";
        case "in_review":
        default:
          return "bg-yellow-100 text-yellow-800";
      }
      
    default:
      return "bg-blue-100 text-blue-800";
  }
};

const formatStatus = (status: string, variant: string) => {
  if (variant === "user-approval") {
    if (status === "true" || status === "approved") return "Approved";
    if (status === "false" || status === "rejected") return "Rejected";
    return "Pending";
  }
  
  if (variant === "lead-approval") {
    if (status === "true") return "Approved";
    if (status === "false") return "Rejected";
    if (status === "undefined") return "Pending";
  }
  
  return status.charAt(0).toUpperCase() + status.slice(1).replace(/_/g, " ");
};

export function StatusBadge({ status, variant = "default" }: StatusBadgeProps) {
  const classes = getStatusClasses(status, variant);
  const formattedStatus = formatStatus(status, variant);
  
  return (
    <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${classes}`}>
      {formattedStatus}
    </span>
  );
}