import { ReactNode, useState } from "react";

export interface Column<T> {
  key: keyof T | string;
  label: string;
  render?: (item: T) => ReactNode;
  sortable?: boolean;
  width?: string;
}

interface DataTableProps<T> {
  data: T[];
  columns: Column<T>[];
  loading?: boolean;
  emptyMessage?: string;
  className?: string;
}

export function DataTable<T extends { _id?: string }>({ 
  data, 
  columns, 
  loading = false, 
  emptyMessage = "No data found",
  className = ""
}: DataTableProps<T>) {
  const [sortColumn, setSortColumn] = useState<keyof T | string | null>(null);
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");

  const handleSort = (columnKey: keyof T | string) => {
    if (sortColumn === columnKey) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortColumn(columnKey);
      setSortDirection("asc");
    }
  };

  const sortedData = [...data].sort((a, b) => {
    if (!sortColumn) return 0;
    
    const aValue = (a as any)[sortColumn];
    const bValue = (b as any)[sortColumn];
    
    if (aValue === bValue) return 0;
    
    const result = aValue > bValue ? 1 : -1;
    return sortDirection === "asc" ? result : -result;
  });

  if (loading) {
    return (
      <div className="animate-pulse">
        <div className="rounded-lg overflow-hidden" style={{ border: '1px solid var(--border-default)' }}>
          <div className="h-12" style={{ backgroundColor: 'var(--surface-2)' }}></div>
          {[...Array(5)].map((_, i) => (
            <div key={i} className="h-16" style={{ borderTop: '1px solid var(--border-subtle)', backgroundColor: 'var(--surface-1)' }}></div>
          ))}
        </div>
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-size-2" style={{ color: 'var(--muted)' }}>{emptyMessage}</p>
      </div>
    );
  }

  return (
    <div className={`overflow-x-auto ${className}`}>
      <table 
        className="min-w-full border-collapse rounded-lg overflow-hidden"
        style={{ 
          border: '1px solid var(--border-default)',
          backgroundColor: 'var(--surface-1)'
        }}
      >
        <thead style={{ backgroundColor: 'var(--surface-2)' }}>
          <tr>
            {columns.map((column) => (
              <th
                key={String(column.key)}
                className={`px-4 py-3 text-left text-size-3 font-semibold ${
                  column.sortable ? "cursor-pointer transition-colors" : ""
                }`}
                style={{ 
                  width: column.width,
                  border: '1px solid var(--border-subtle)',
                  color: 'var(--foreground)'
                }}
                onClick={() => column.sortable && handleSort(column.key)}
                onMouseEnter={(e) => {
                  if (column.sortable) {
                    e.currentTarget.style.backgroundColor = 'var(--surface-3)';
                  }
                }}
                onMouseLeave={(e) => {
                  if (column.sortable) {
                    e.currentTarget.style.backgroundColor = 'var(--surface-2)';
                  }
                }}
              >
                <div className="flex items-center gap-2">
                  {column.label}
                  {column.sortable && sortColumn === column.key && (
                    <span className="text-size-4" style={{ color: 'var(--accent)' }}>
                      {sortDirection === "asc" ? "↑" : "↓"}
                    </span>
                  )}
                </div>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {sortedData.map((item, index) => (
            <tr
              key={item._id || index}
              className="transition-colors"
              style={{ backgroundColor: 'var(--surface-1)' }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--surface-2)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--surface-1)';
              }}
            >
              {columns.map((column) => (
                <td
                  key={String(column.key)}
                  className="px-4 py-3 text-size-3"
                  style={{ 
                    border: '1px solid var(--border-subtle)',
                    color: 'var(--foreground)'
                  }}
                >
                  {column.render 
                    ? column.render(item)
                    : String((item as any)[column.key] || "-")
                  }
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}