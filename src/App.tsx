import { Authenticated, Unauthenticated, useQuery, useMutation } from "convex/react";
import { SignInButton, UserButton } from "@clerk/clerk-react";
import { api } from "../convex/_generated/api";
import { Toaster, toast } from "sonner";
import { useState, useEffect } from "react";
import React from "react";
import { Dashboard } from "./components/Dashboard";
import { ReferralLinks } from "./components/ReferralLinks";
import { Leads } from "./components/Leads";
import { Deals } from "./components/Deals";
import { Earnings } from "./components/Earnings";
import { AdminPanel } from "./components/AdminPanel";
import { ProfileSetup } from "./components/ProfileSetup";
import { PublicLeadForm } from "./components/PublicLeadForm";
import { InvoiceEditorPage } from "./components/InvoiceEditorPage";

export default function App() {
  const [currentRoute, setCurrentRoute] = useState(window.location.pathname);

  useEffect(() => {
    const handlePopState = () => {
      setCurrentRoute(window.location.pathname);
    };

    const handleToast = (event: any) => {
      const { message, type } = event.detail;
      if (type === 'success') {
        toast.success(message);
      } else if (type === 'error') {
        toast.error(message);
      } else {
        toast(message);
      }
    };

    window.addEventListener('popstate', handlePopState);
    window.addEventListener('show-toast', handleToast);
    
    return () => {
      window.removeEventListener('popstate', handlePopState);
      window.removeEventListener('show-toast', handleToast);
    };
  }, []);

  // Handle public lead submission route
  if (currentRoute === '/submit-lead') {
    return (
      <>
        <PublicLeadForm />
        <Toaster />
      </>
    );
  }

  return (
    <div className="min-h-screen flex flex-col bg-background">
      {/* Header */}
      <header className="sticky top-0 z-50 bg-surface-1/95 backdrop-blur-md border-b border-border">
        <div className="max-w-7xl mx-auto px-grid-4 sm:px-grid-6 lg:px-grid-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo/Brand */}
            <div className="flex items-center gap-grid-3">
              <img
                src="/logo.svg"
                alt="Partners Logo"
                className="w-8 h-8 object-contain"
              />
              <h2 className="text-size-2 hidden sm:block">Partners Dashboard</h2>
              <h2 className="text-size-3 sm:hidden">Dashboard</h2>
            </div>

            {/* User Actions */}
            <Authenticated>
              <AdminNavButtons />
            </Authenticated>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="flex-1 bg-background">
        <Content />
      </main>

      {/* Toast Notifications */}
      <Toaster />
    </div>
  );
}

function AdminNavButtons() {
  return (
    <div className="flex items-center gap-grid-2">
      <AdminNavButton />
      <UserButton />
    </div>
  );
}

function AdminNavButton() {
  const user = useQuery(api.users.myProfile);
  
  if (!user?.role || !["admin", "superadmin", "ops", "accounting"].includes(user.role)) {
    return null;
  }

  const handleAdminClick = () => {
    // Use the proper React way to navigate
    const event = new CustomEvent('adminNavClick');
    window.dispatchEvent(event);
  };

  return (
    <button
      onClick={handleAdminClick}
      className="flex items-center gap-grid-2 px-grid-4 py-grid-2 bg-accent text-accent-foreground hover:bg-accent/90 rounded-lg transition-colors text-size-4 font-normal"
    >
      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
      <span className="hidden sm:inline">Admin</span>
      <span className="sm:hidden">Mgmt</span>
    </button>
  );
}

function Content() {
  const [activeTab, setActiveTab] = useState("dashboard");
  const [invoiceEditorState, setInvoiceEditorState] = useState<{
    isEditing: boolean;
    deal: any | null;
  }>({
    isEditing: false,
    deal: null
  });

  const showInvoiceEditor = (deal: any) => {
    setInvoiceEditorState({
      isEditing: true,
      deal
    });
  };

  const hideInvoiceEditor = () => {
    setInvoiceEditorState({
      isEditing: false,
      deal: null
    });
    setActiveTab("invoices");
  };

  return (
    <div className="min-h-[calc(100vh-4rem)]">
      <Unauthenticated>
        {/* Enhanced Login Page */}
        <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] px-4 sm:px-6 lg:px-8">
          <div className="w-full max-w-md space-y-grid-8">
            {/* Hero Section */}
            <div className="text-center space-y-grid-4">
              <div className="mx-auto w-16 h-16 bg-primary rounded-2xl flex items-center justify-center mb-grid-6">
                <img
                  src="/logo.svg"
                  alt="Partners Logo"
                  className="w-12 h-12 object-contain"
                />
              </div>
              <div className="space-y-grid-2">
                <h1 className="text-size-1 font-semibold text-foreground">Partners Dashboard</h1>
                <p className="text-size-3 text-muted-foreground">Secure access for verified partners</p>
              </div>
            </div>

            {/* Sign In Button */}
            <div className="card">
              <div className="card-content">
                <div className="w-full space-y-grid-6">
                  {/* Form Header */}
                  <div className="text-center space-y-grid-2">
                    <h2 className="text-size-2 text-foreground">Welcome back</h2>
                    <p className="text-size-4 text-muted">Sign in to access your partner dashboard</p>
                  </div>

                  {/* Clerk Sign In Button */}
                  <div className="flex justify-center">
                    <SignInButton mode="modal">
                      <button className="auth-button">
                        Sign in to Partners Dashboard
                      </button>
                    </SignInButton>
                  </div>

                  {/* Help Text */}
                  <div className="text-center">
                    <p className="text-size-4 text-muted">
                      Need help? Contact your IBC Project Manager
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </Unauthenticated>

      <Authenticated>
        <AuthenticatedContent 
          activeTab={activeTab} 
          setActiveTab={setActiveTab}
          invoiceEditorState={invoiceEditorState}
          showInvoiceEditor={showInvoiceEditor}
          hideInvoiceEditor={hideInvoiceEditor}
        />
      </Authenticated>
    </div>
  );
}

function AuthenticatedContent({ 
  activeTab, 
  setActiveTab,
  invoiceEditorState,
  showInvoiceEditor,
  hideInvoiceEditor
}: { 
  activeTab: string; 
  setActiveTab: (tab: string) => void;
  invoiceEditorState: { isEditing: boolean; deal: any | null };
  showInvoiceEditor: (deal: any) => void;
  hideInvoiceEditor: () => void;
}) {
  const user = useQuery(api.users.myProfile);
  const upsertUser = useMutation(api.users.upsertFromAuth);

  // Listen for admin navigation event
  React.useEffect(() => {
    const handleAdminNav = () => {
      setActiveTab("admin");
    };
    
    window.addEventListener('adminNavClick', handleAdminNav);
    return () => window.removeEventListener('adminNavClick', handleAdminNav);
  }, [setActiveTab]);

  // Handle loading state
  if (user === undefined) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          <p className="text-size-4 text-muted">Loading your profile...</p>
        </div>
      </div>
    );
  }

  // Handle case where user needs to be created/updated
  if (user === null) {
    // Auto-create user record
    upsertUser();
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="flex flex-col items-center gap-grid-4">
          <div className="animate-spin rounded-full h-8 w-8 border-2 border-primary border-t-transparent"></div>
          <p className="text-size-4 text-muted">Setting up your account...</p>
        </div>
      </div>
    );
  }

  // Force profile completion for new users
  if (!user.profileCompleted) {
    return <ProfileSetup />;
  }

  // Handle pending approval
  if (!user.approved) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-4rem)] px-4 sm:px-6 lg:px-8">
        <div className="w-full max-w-2xl">
          <div className="card">
            <div className="card-content text-center space-y-grid-6">
              {/* Status Icon */}
              <div className="mx-auto w-16 h-16 bg-accent/10 rounded-2xl flex items-center justify-center">
                <svg className="w-8 h-8 text-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>

              {/* Content */}
              <div className="space-y-grid-4">
                <h2 className="text-size-1 text-foreground">Access Pending</h2>
                <p className="text-size-3 text-muted max-w-md mx-auto">
                  Your account is pending approval. Contact your IBC Project Manager for access.
                </p>
              </div>

              {/* User Details Card */}
              <div className="bg-accent/5 border border-accent/20 rounded-lg p-grid-4 text-left">
                <h3 className="text-size-4 font-semibold text-foreground mb-grid-2">Your Details</h3>
                <div className="space-y-1 text-size-4 text-muted">
                  <p><span className="font-medium">Email:</span> {user.email}</p>
                  <p><span className="font-medium">Company:</span> {user.companyName}</p>
                  {user.defaultReferralCode && (
                    <p><span className="font-medium">Referral Code:</span> {user.defaultReferralCode}</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show Invoice Editor Page if in editing mode
  if (invoiceEditorState.isEditing && invoiceEditorState.deal) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-grid-6 lg:py-grid-8">
        <InvoiceEditorPage
          deal={invoiceEditorState.deal}
          onBack={hideInvoiceEditor}
        />
      </div>
    );
  }

  // Main dashboard for approved users
  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-grid-6 lg:py-grid-8">
      {/* Welcome Header with Integrated Navigation */}
      <div className="mb-grid-8">
        <div className="flex flex-col xl:flex-row xl:items-center xl:justify-between gap-grid-6">
          {/* Welcome Section */}
          <div className="space-y-grid-3">
            <h1 className="text-size-1 text-foreground">
              Welcome back, {user?.name || user?.email?.split('@')[0]}
            </h1>
            <div className="flex flex-wrap items-center gap-x-grid-6 gap-y-grid-2 text-size-3">
              <span className="status-badge status-badge-role">
                <span className="w-3 h-3 bg-accent rounded-full"></span>
                <span className="font-medium text-foreground">{user?.role ? user.role.charAt(0).toUpperCase() + user.role.slice(1) : 'User'}</span>
              </span>
              {user?.tier && (
                <span className={`status-badge tier-badge tier-badge-${user.tier}`}>
                  <span className="w-3 h-3 bg-primary rounded-full"></span>
                  <span className="font-medium text-foreground">Tier: {user.tier.charAt(0).toUpperCase() + user.tier.slice(1)}</span>
                </span>
              )}
              {user?.defaultReferralCode && (
                <button
                  onClick={() => {
                    const referralUrl = `${window.location.origin}/submit-lead?ref=${user.defaultReferralCode}`;
                    navigator.clipboard.writeText(referralUrl);
                    // Show success feedback
                    const event = new CustomEvent('show-toast', { 
                      detail: { message: 'Referral link copied to clipboard!', type: 'success' } 
                    });
                    window.dispatchEvent(event);
                  }}
                  className="status-badge status-badge-ref hover:bg-accent/10 rounded-lg px-grid-2 py-grid-1 transition-all group"
                >
                  <span className="w-3 h-3 bg-secondary rounded-full"></span>
                  <span className="font-semibold text-foreground">Ref Link: {user.defaultReferralCode.charAt(0).toUpperCase() + user.defaultReferralCode.slice(1)}</span>
                  <svg className="w-4 h-4 text-foreground/60 group-hover:text-foreground transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                  </svg>
                </button>
              )}
            </div>
          </div>

          {/* Navigation Tabs */}
          <nav className="xl:flex-shrink-0">
            <div className="card">
              <div className="p-grid-2">
                <div className="flex flex-wrap gap-grid-1">
                  {[
                    { id: "dashboard", label: "Dashboard", icon: "📊" },
                    { id: "referrals", label: "Referral Links", icon: "🔗" },
                    { id: "leads", label: "Leads", icon: "👥" },
                    { id: "deals", label: "Deals", icon: "💼" },
                    { id: "earnings", label: "Earnings", icon: "💰" },
                  ].map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`nav-tab ${activeTab === tab.id ? 'active' : ''}
                        flex items-center gap-grid-2 min-w-0 flex-shrink-0`}
                    >
                      <span className="text-size-4">{tab.icon}</span>
                      <span className="hidden sm:inline xl:hidden 2xl:inline">{tab.label}</span>
                      <span className="sm:hidden xl:inline 2xl:hidden text-size-4">{tab.label.split(' ')[0]}</span>
                    </button>
                  ))}
                </div>
              </div>
            </div>
          </nav>
        </div>
      </div>

      {/* Content Area */}
      <div className="card">
        <div className="card-content">
          {activeTab === "dashboard" && <Dashboard setActiveTab={setActiveTab} />}
          {activeTab === "referrals" && <ReferralLinks />}
          {activeTab === "leads" && <Leads />}
          {activeTab === "deals" && <Deals setActiveTab={setActiveTab} showInvoiceEditor={showInvoiceEditor} />}
          {activeTab === "earnings" && <Earnings />}
          {activeTab === "admin" && user?.role && ["admin", "superadmin", "ops", "accounting"].includes(user.role) && <AdminPanel />}
        </div>
      </div>
    </div>
  );
}
